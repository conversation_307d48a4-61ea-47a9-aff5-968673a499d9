import type { As, Options, Props } from "../utils/types.js";
import type { CompositeStore } from "./composite-store.js";
/**
 * Returns props to create a `CompositeTypeahead` component.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const store = useCompositeStore();
 * const props = useCompositeTypeahead({ store });
 * <Composite store={store} {...props}>
 *   <CompositeItem>Item 1</CompositeItem>
 *   <CompositeItem>Item 2</CompositeItem>
 * </Composite>
 * ```
 */
export declare const useCompositeTypeahead: import("../utils/types.js").Hook<CompositeTypeaheadOptions<"div">>;
/**
 * Renders a component that adds typeahead functionality to composite
 * components.
 *
 * When the
 * [`typeahead`](https://ariakit.org/reference/composite-typeahead#typeahead)
 * prop is enabled, which it is by default, hitting printable character keys
 * will move focus to the next composite item that begins with the input
 * characters.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * <CompositeProvider>
 *   <Composite render={<CompositeTypeahead />}>
 *     <CompositeItem>Item 1</CompositeItem>
 *     <CompositeItem>Item 2</CompositeItem>
 *   </Composite>
 * </CompositeProvider>
 * ```
 */
export declare const CompositeTypeahead: import("../utils/types.js").Component<CompositeTypeaheadOptions<"div">>;
export interface CompositeTypeaheadOptions<T extends As = "div"> extends Options<T> {
    /**
     * Object returned by the
     * [`useCompositeStore`](https://ariakit.org/reference/use-composite-store)
     * hook. If not provided, the closest
     * [`Composite`](https://ariakit.org/reference/composite) or
     * [`CompositeProvider`](https://ariakit.org/reference/composite-provider)
     * components' context will be used.
     */
    store?: CompositeStore;
    /**
     * When enabled, pressing printable character keys will move focus to the next
     * composite item that starts with the entered characters.
     * @default true
     */
    typeahead?: boolean;
}
export type CompositeTypeaheadProps<T extends As = "div"> = Props<CompositeTypeaheadOptions<T>>;
