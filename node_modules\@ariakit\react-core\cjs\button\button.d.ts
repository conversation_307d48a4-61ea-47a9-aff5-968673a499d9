import type { CommandOptions } from "../command/command.js";
import type { As, Props } from "../utils/types.js";
/**
 * Returns props to create a `Button` component. If the element is not a native
 * button, the hook will return additional props to make sure it's accessible.
 * @see https://ariakit.org/components/button
 * @example
 * ```jsx
 * const props = useButton({ render: <div /> });
 * <Role {...props}>Accessible button</Role>
 * ```
 */
export declare const useButton: import("../utils/types.js").Hook<ButtonOptions<"button">>;
/**
 * Renders an accessible button element. If the underlying element is not a
 * native button, this component will pass additional attributes to make sure
 * it's accessible.
 * @see https://ariakit.org/components/button
 * @example
 * ```jsx
 * <Button>Button</Button>
 * ```
 */
export declare const Button: import("../utils/types.js").Component<ButtonOptions<"button">>;
export type ButtonOptions<T extends As = "button"> = CommandOptions<T>;
export type ButtonProps<T extends As = "button"> = Props<ButtonOptions<T>>;
