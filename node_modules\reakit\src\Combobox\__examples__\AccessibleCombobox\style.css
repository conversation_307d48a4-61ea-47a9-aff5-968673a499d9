:root {
  --font-family: var(--font-family-body, sans-serif);
  --font-size: var(--font-size-body, 16px);
  --combobox-background: var(--color-background-50, #fff);
  --combobox-color: var(--color-grayscale-700, #333);
  --combobox-border: var(--color-alpha-500, rgba(0, 0, 0, 0.5));
  --combobox-border-focus: var(--color-primary-700, #1976d2);
  --listbox-background: var(--color-background-200, #fff);
  --listbox-color: var(--color-grayscale-700, #333);
  --listbox-shadow-50: var(--color-shadow-50, rgba(0, 0, 0, 0.05));
  --listbox-shadow-100: var(--color-shadow-50, rgba(0, 0, 0, 0.1));
  --option-background-hover: var(--color-primary-50, #e3f2fd);
  --option-background-focus: var(--color-primary-100, #bbdefb);
}

[role="combobox"] {
  font-family: var(--font-family);
  font-size: var(--font-size);
  background-color: var(--combobox-background);
  color: var(--combobox-color);
  border: 1px solid var(--combobox-border);
  border-radius: 4px;
  height: 2.5em;
  padding: 0 1em;
  outline: 0;
  width: 250px;
  box-sizing: border-box;
}

[role="combobox"]:focus {
  border-color: var(--combobox-border-focus);
  box-shadow: 0 0 0 1px var(--combobox-border-focus);
}

[role="listbox"] {
  font-family: var(--font-family);
  font-size: var(--font-size);
  background-color: var(--listbox-background);
  color: var(--listbox-color);
  width: 250px;
  z-index: 999;
  padding: 1em;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow:
    0 0 8px var(--listbox-shadow-50),
    0 10px 10px -5px var(--listbox-shadow-50),
    0 20px 25px -5px var(--listbox-shadow-100);
}

[role="option"] {
  cursor: default;
  padding: 0.5em;
  margin: 0 -0.5em;
  border-radius: 4px;
}

[role="option"]:first-child {
  margin-top: -0.5em;
}

[role="option"]:last-child {
  margin-bottom: -0.5em;
}

[role="option"]:hover {
  background-color: var(--option-background-hover);
}

[role="combobox"]:focus + [role="listbox"] [aria-selected="true"] {
  background-color: var(--option-background-focus);
}
