"use client";
import {
  createStore,
  mergeStore,
  omit,
  setup,
  subscribe,
  sync,
  throwOnConflictingProps
} from "./EAHJFCU4.js";
import {
  defaultValue
} from "./Y3OOHFCN.js";
import {
  __spreadProps,
  __spreadValues
} from "./4R3V3JGP.js";

// src/disclosure/disclosure-store.ts
function createDisclosureStore(props = {}) {
  const store = mergeStore(
    props.store,
    omit(props.disclosure, ["contentElement", "disclosureElement"])
  );
  throwOnConflictingProps(props, store);
  const syncState = store == null ? void 0 : store.getState();
  const open = defaultValue(
    props.open,
    syncState == null ? void 0 : syncState.open,
    props.defaultOpen,
    false
  );
  const animated = defaultValue(props.animated, syncState == null ? void 0 : syncState.animated, false);
  const initialState = {
    open,
    animated,
    animating: !!animated && open,
    mounted: open,
    contentElement: defaultValue(syncState == null ? void 0 : syncState.contentElement, null),
    disclosureElement: defaultValue(syncState == null ? void 0 : syncState.disclosureElement, null)
  };
  const disclosure = createStore(initialState, store);
  setup(
    disclosure,
    () => sync(disclosure, ["animated", "animating"], (state) => {
      if (state.animated)
        return;
      disclosure.setState("animating", false);
    })
  );
  setup(
    disclosure,
    () => subscribe(disclosure, ["open"], () => {
      if (!disclosure.getState().animated)
        return;
      disclosure.setState("animating", true);
    })
  );
  setup(
    disclosure,
    () => sync(disclosure, ["open", "animating"], (state) => {
      disclosure.setState("mounted", state.open || state.animating);
    })
  );
  return __spreadProps(__spreadValues({}, disclosure), {
    setOpen: (value) => disclosure.setState("open", value),
    show: () => disclosure.setState("open", true),
    hide: () => disclosure.setState("open", false),
    toggle: () => disclosure.setState("open", (open2) => !open2),
    stopAnimation: () => disclosure.setState("animating", false),
    setContentElement: (value) => disclosure.setState("contentElement", value),
    setDisclosureElement: (value) => disclosure.setState("disclosureElement", value)
  });
}

export {
  createDisclosureStore
};
