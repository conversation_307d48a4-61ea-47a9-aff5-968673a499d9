import type { AriaAttri<PERSON>es, ChangeEvent, MouseEvent, KeyboardEvent as ReactKeyboardEvent } from "react";
import type { BooleanOrCallback, StringWithValue } from "@ariakit/core/utils/types";
import type { CompositeOptions } from "../composite/composite.js";
import type { PopoverAnchorOptions } from "../popover/popover-anchor.js";
import type { As, Props } from "../utils/types.js";
import type { ComboboxStore, ComboboxStoreState } from "./combobox-store.js";
/**
 * Returns props to create a `Combobox` component.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore();
 * const props = useCombobox({ store });
 * <Role {...props} />
 * <ComboboxPopover store={store}>
 *   <ComboboxItem value="Apple" />
 *   <ComboboxItem value="Banana" />
 *   <ComboboxItem value="Orange" />
 * </ComboboxPopover>
 * ```
 */
export declare const useCombobox: import("../utils/types.js").Hook<ComboboxOptions<"input">>;
/**
 * Renders a combobox input element that can be used to filter a list of items.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {2}
 * <ComboboxProvider>
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Apple" />
 *     <ComboboxItem value="Banana" />
 *     <ComboboxItem value="Orange" />
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const Combobox: import("../utils/types.js").Component<ComboboxOptions<"input">>;
export interface ComboboxOptions<T extends As = "input"> extends CompositeOptions<T>, PopoverAnchorOptions<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxProvider`](https://ariakit.org/reference/combobox-provider)
     * component's context will be used.
     */
    store?: ComboboxStore;
    /**
     * Whether the first enabled item will be automatically selected when the
     * combobox input value changes. When it's set to `true`, the exact behavior
     * will depend on the value of the
     * [`autoComplete`](https://ariakit.org/reference/combobox#autocomplete) prop:
     * - If [`autoComplete`](https://ariakit.org/reference/combobox#autocomplete)
     *   is `both` or `inline`, the first item is automatically focused when the
     *   popup opens, and the input value changes to reflect this. The inline
     *   completion string will be highlighted and will have a selected state.
     * - If [`autoComplete`](https://ariakit.org/reference/combobox#autocomplete)
     *   is `list` or `none`, the first item is automatically focused when the
     *   popup opens, but the input value doesn't change.
     *
     * To change which item is auto selected, use the
     * [`getAutoSelectId`](https://ariakit.org/reference/combobox#getautoselectid)
     * prop.
     *
     * Live examples:
     * - [Combobox with integrated
     *   filter](https://ariakit.org/examples/combobox-filtering-integrated)
     * - [ComboboxGroup](https://ariakit.org/examples/combobox-group)
     * - [Combobox with links](https://ariakit.org/examples/combobox-links)
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * - [Menu with Combobox](https://ariakit.org/examples/menu-combobox)
     * - [Select with Combobox](https://ariakit.org/examples/select-combobox)
     * @default false
     */
    autoSelect?: boolean;
    /**
     * Function that takes the currently rendered items and returns the id of the
     * item to be auto selected when the
     * [`autoSelect`](https://ariakit.org/reference/combobox#autoselect) prop is
     * `true`.
     *
     * By default, the first enabled item is auto selected. This function is handy
     * if you prefer a different item to be auto selected.
     *
     * Live examples:
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     * @example
     * ```jsx
     * <Combobox
     *   autoSelect
     *   getAutoSelectId={(items) => {
     *     // Auto select the first enabled item with a value
     *     const item = items.find((item) => {
     *       if (item.disabled) return false;
     *       if (!item.value) return false;
     *       return true;
     *     });
     *     return item?.id;
     *   }}
     * />
     * ```
     */
    getAutoSelectId?: (renderedItems: ComboboxStoreState["renderedItems"]) => string | null | undefined;
    /**
     * Whether the items will be filtered based on
     * [`value`](https://ariakit.org/reference/combobox-provider#value) and
     * whether the input value will temporarily change based on the active item.
     *
     * This prop is based on the standard
     * [`aria-autocomplete`](https://w3c.github.io/aria/#aria-autocomplete)
     * attribute, accepting the same values:
     * - `list` (default): indicates that the items will be dynamically rendered
     *   based on [`value`](https://ariakit.org/reference/combobox-provider#value)
     *   and the input value will _not_ change based on the active item. The
     *   filtering logic must be implemented by the consumer of this component.
     * - `inline`: indicates that the items are static, that is, they won't be
     *   filtered, but the input value will temporarily change based on the active
     *   item. Ariakit will automatically provide the inline autocompletion
     *   behavior.
     * - `both`: indicates that the items will be dynamically rendered based on
     *   [`value`](https://ariakit.org/reference/combobox-provider#value) and the
     *   input value will temporarily change based on the active item. The
     *   filtering logic must be implemented by the consumer of this component,
     *   whereas Ariakit will automatically provide the inline autocompletion
     *   behavior.
     * - `none`: the items are static and the input value will _not_ change based
     *   on the active item.
     *
     * Live examples:
     * - [ComboboxGroup](https://ariakit.org/examples/combobox-group)
     * @default "list"
     */
    autoComplete?: StringWithValue<Required<AriaAttributes>["aria-autocomplete"]>;
    /**
     * Whether the [`ComboboxList`](https://ariakit.org/reference/combobox-list)
     * or [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components should be shown when the input value changes.
     *
     * Live examples:
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * @default true
     * @example
     * ```jsx
     * <Combobox showOnChange={(event) => event.target.value.length > 1} />
     * ```
     */
    showOnChange?: BooleanOrCallback<ChangeEvent<HTMLElement>>;
    /**
     * Whether the [`ComboboxList`](https://ariakit.org/reference/combobox-list)
     * or [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components should be shown when the input is clicked.
     *
     * Live examples:
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * @default true
     * @example
     * ```jsx
     * <Combobox showOnMouseDown={value.length > 1} />
     * ```
     */
    showOnMouseDown?: BooleanOrCallback<MouseEvent<HTMLElement>>;
    /**
     * Whether the [`ComboboxList`](https://ariakit.org/reference/combobox-list)
     * or [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components should be shown when the user presses the arrow up or down keys
     * while focusing on the combobox input element.
     *
     * Live examples:
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * @default true
     * @example
     * ```jsx
     * <Combobox showOnKeyDown={value.length > 1} />
     * ```
     */
    showOnKeyDown?: BooleanOrCallback<ReactKeyboardEvent<HTMLElement>>;
    /**
     * Whether the combobox
     * [`value`](https://ariakit.org/reference/combobox-provider#value) state
     * should be updated when the input value changes. This is useful if you want
     * to customize how the store
     * [`value`](https://ariakit.org/reference/combobox-provider#value) is updated
     * based on the input element's value.
     *
     * Live examples:
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * @default true
     */
    setValueOnChange?: BooleanOrCallback<ChangeEvent<HTMLElement>>;
    /**
     * Whether the combobox
     * [`value`](https://ariakit.org/reference/combobox-provider#value) state
     * should be updated when the combobox input element gets clicked. This
     * usually only applies when
     * [`autoComplete`](https://ariakit.org/reference/combobox#autocomplete) is
     * `both` or `inline`, because the input value will temporarily change based
     * on the active item and the store
     * [`value`](https://ariakit.org/reference/combobox-provider#value) will not
     * be updated until the user confirms the selection.
     * @default true
     */
    setValueOnClick?: BooleanOrCallback<MouseEvent<HTMLElement>>;
}
export type ComboboxProps<T extends As = "input"> = Props<ComboboxOptions<T>>;
