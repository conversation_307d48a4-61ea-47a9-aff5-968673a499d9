import type { ReactElement, ReactNode } from "react";
import type { PickRequired } from "@ariakit/core/utils/types";
import type { CompositeStoreItem, CompositeStoreProps } from "./composite-store.js";
/**
 * Provides a composite store to
 * [`CompositeItem`](https://ariakit.org/reference/composite-item) components.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * <CompositeProvider>
 *   <Composite>
 *     <CompositeItem />
 *     <CompositeItem />
 *     <CompositeItem />
 *   </Composite>
 * </CompositeProvider>
 * ```
 */
export declare function CompositeProvider<T extends CompositeStoreItem = CompositeStoreItem>(props: PickRequired<CompositeProviderProps<T>, "items" | "defaultItems">): ReactElement;
export declare function CompositeProvider(props?: CompositeProviderProps): ReactElement;
export interface CompositeProviderProps<T extends CompositeStoreItem = CompositeStoreItem> extends CompositeStoreProps<T> {
    children?: ReactNode;
}
