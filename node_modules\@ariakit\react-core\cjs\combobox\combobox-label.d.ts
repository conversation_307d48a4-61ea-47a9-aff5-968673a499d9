import type { As, Options, Props } from "../utils/types.js";
import type { ComboboxStore } from "./combobox-store.js";
/**
 * Returns props to create a `ComboboxLabel` component.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore();
 * const props = useComboboxLabel({ store });
 * <Role {...props}>Favorite fruit</Role>
 * <Combobox store={store} />
 * ```
 */
export declare const useComboboxLabel: import("../utils/types.js").Hook<ComboboxLabelOptions<"label">>;
/**
 * Renders a label for the [`Combobox`](https://ariakit.org/reference/combobox)
 * component.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {2}
 * <ComboboxProvider>
 *   <ComboboxLabel>Favorite fruit</ComboboxLabel>
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Apple" />
 *     <ComboboxItem value="Orange" />
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const ComboboxLabel: import("../utils/types.js").Component<ComboboxLabelOptions<"label">>;
export interface ComboboxLabelOptions<T extends As = "label"> extends Options<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxProvider`](https://ariakit.org/reference/combobox-provider)
     * component's context will be used.
     */
    store?: ComboboxStore;
}
export type ComboboxLabelProps<T extends As = "label"> = Props<ComboboxLabelOptions<T>>;
