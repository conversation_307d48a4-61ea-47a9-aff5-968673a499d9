"use client";
import {
  isApple
} from "../__chunks/MHPO2BXA.js";
import {
  contains
} from "../__chunks/DLOEKDPY.js";
import {
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/utils/events.ts
function isPortalEvent(event) {
  return Boolean(
    event.currentTarget && !contains(event.currentTarget, event.target)
  );
}
function isSelfTarget(event) {
  return event.target === event.currentTarget;
}
function isOpeningInNewTab(event) {
  const element = event.currentTarget;
  if (!element)
    return false;
  const isAppleDevice = isApple();
  if (isAppleDevice && !event.metaKey)
    return false;
  if (!isAppleDevice && !event.ctrlKey)
    return false;
  const tagName = element.tagName.toLowerCase();
  if (tagName === "a")
    return true;
  if (tagName === "button" && element.type === "submit")
    return true;
  if (tagName === "input" && element.type === "submit")
    return true;
  return false;
}
function isDownloading(event) {
  const element = event.currentTarget;
  if (!element)
    return false;
  const tagName = element.tagName.toLowerCase();
  if (!event.altKey)
    return false;
  if (tagName === "a")
    return true;
  if (tagName === "button" && element.type === "submit")
    return true;
  if (tagName === "input" && element.type === "submit")
    return true;
  return false;
}
function fireEvent(element, type, eventInit) {
  const event = new Event(type, eventInit);
  return element.dispatchEvent(event);
}
function fireBlurEvent(element, eventInit) {
  const event = new FocusEvent("blur", eventInit);
  const defaultAllowed = element.dispatchEvent(event);
  const bubbleInit = __spreadProps(__spreadValues({}, eventInit), { bubbles: true });
  element.dispatchEvent(new FocusEvent("focusout", bubbleInit));
  return defaultAllowed;
}
function fireFocusEvent(element, eventInit) {
  const event = new FocusEvent("focus", eventInit);
  const defaultAllowed = element.dispatchEvent(event);
  const bubbleInit = __spreadProps(__spreadValues({}, eventInit), { bubbles: true });
  element.dispatchEvent(new FocusEvent("focusin", bubbleInit));
  return defaultAllowed;
}
function fireKeyboardEvent(element, type, eventInit) {
  const event = new KeyboardEvent(type, eventInit);
  return element.dispatchEvent(event);
}
function fireClickEvent(element, eventInit) {
  const event = new MouseEvent("click", eventInit);
  return element.dispatchEvent(event);
}
function isFocusEventOutside(event, container) {
  const containerElement = container || event.currentTarget;
  const relatedTarget = event.relatedTarget;
  return !relatedTarget || !contains(containerElement, relatedTarget);
}
function queueBeforeEvent(element, type, callback) {
  const raf = requestAnimationFrame(() => {
    element.removeEventListener(type, callImmediately, true);
    callback();
  });
  const callImmediately = () => {
    cancelAnimationFrame(raf);
    callback();
  };
  element.addEventListener(type, callImmediately, {
    once: true,
    capture: true
  });
  return raf;
}
function addGlobalEventListener(type, listener, options, scope = window) {
  const children = [];
  try {
    scope.document.addEventListener(type, listener, options);
    for (const frame of Array.from(scope.frames)) {
      children.push(addGlobalEventListener(type, listener, options, frame));
    }
  } catch (e) {
  }
  const removeEventListener = () => {
    try {
      scope.document.removeEventListener(type, listener, options);
    } catch (e) {
    }
    children.forEach((remove) => remove());
  };
  return removeEventListener;
}
export {
  addGlobalEventListener,
  fireBlurEvent,
  fireClickEvent,
  fireEvent,
  fireFocusEvent,
  fireKeyboardEvent,
  isDownloading,
  isFocusEventOutside,
  isOpeningInNewTab,
  isPortalEvent,
  isSelfTarget,
  queueBeforeEvent
};
