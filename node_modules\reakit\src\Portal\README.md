---
path: /docs/portal/
redirect_from:
  - /components/portal/
---

# Portal

`Portal` is an abstract wrapper component that uses [React Portals](https://reactjs.org/docs/portals.html) underneath. It can be used to put anything in a portal and supports nested portals.

<carbon-ad></carbon-ad>

## Installation

```sh
npm install reakit
```

Learn more in [Get started](/docs/get-started/).

## Usage

```jsx
import { Portal } from "reakit/Portal";

function Example() {
  return (
    <div style={{ background: "red", color: "white" }}>
      I am here, <Portal>but I am detached at the bottom of the page.</Portal>
    </div>
  );
}
```
