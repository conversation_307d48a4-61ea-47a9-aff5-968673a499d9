"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _5UTRYT6Ecjs = require('../__chunks/5UTRYT6E.cjs');


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/DAJUUBUI.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');


var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-cancel.tsx
var _misc = require('@ariakit/core/utils/misc');
var _jsxruntime = require('react/jsx-runtime');
var children = /* @__PURE__ */ _jsxruntime.jsxs.call(void 0, 
  "svg",
  {
    "aria-hidden": "true",
    display: "block",
    viewBox: "0 0 16 16",
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "1pt",
    width: "1em",
    height: "1em",
    pointerEvents: "none",
    children: [
      /* @__PURE__ */ _jsxruntime.jsx.call(void 0, "line", { x1: "5", y1: "5", x2: "11", y2: "11" }),
      /* @__PURE__ */ _jsxruntime.jsx.call(void 0, "line", { x1: "5", y1: "11", x2: "11", y2: "5" })
    ]
  }
);
var useComboboxCancel = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store"]);
    const context = _3WCBE6SUcjs.useComboboxProviderContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxCancel must receive a `store` prop or be wrapped in a ComboboxProvider component."
    );
    const onClickProp = props.onClick;
    const onClick = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onClickProp == null ? void 0 : onClickProp(event);
      if (event.defaultPrevented)
        return;
      store == null ? void 0 : store.setValue("");
      store == null ? void 0 : store.move(null);
    });
    const comboboxId = store.useState((state) => {
      var _a2;
      return (_a2 = state.baseElement) == null ? void 0 : _a2.id;
    });
    props = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      children,
      "aria-label": "Clear input",
      // This aria-controls will ensure the combobox popup remains visible when
      // this element gets focused. This logic is done in the ComboboxPopover
      // component.
      "aria-controls": comboboxId
    }, props), {
      onClick
    });
    props = _5UTRYT6Ecjs.useButton.call(void 0, props);
    return props;
  }
);
var ComboboxCancel = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxCancel(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "button", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxCancel.displayName = "ComboboxCancel";
}



exports.ComboboxCancel = ComboboxCancel; exports.useComboboxCancel = useComboboxCancel;
