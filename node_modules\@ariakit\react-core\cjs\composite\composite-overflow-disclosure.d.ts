import type { PopoverDisclosureOptions } from "../popover/popover-disclosure.js";
import type { As, Props } from "../utils/types.js";
import type { CompositeItemOptions } from "./composite-item.js";
import type { CompositeOverflowStore } from "./composite-overflow-store.js";
/**
 * Returns props to create a `CompositeOverflowDisclosure` component. This hook
 * should be used in a component that's wrapped with a composite component.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * // This component should be wrapped with Composite
 * const props = useCompositeOverflowDisclosure();
 * <Role {...props}>+2 items</Role>
 * ```
 */
export declare const useCompositeOverflowDisclosure: import("../utils/types.js").Hook<CompositeOverflowDisclosureOptions<"button">>;
/**
 * Renders a disclosure button for the `CompositeOverflow` component. This
 * component should be wrapped with a composite component.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const composite = useCompositeStore();
 * const overflow = useCompositeOverflowStore();
 * <Composite store={composite}>
 *   <CompositeItem>Item 1</CompositeItem>
 *   <CompositeItem>Item 2</CompositeItem>
 *   <CompositeOverflowDisclosure store={overflow}>
 *     +2 items
 *   </CompositeOverflowDisclosure>
 *   <CompositeOverflow store={overflow}>
 *     <CompositeItem>Item 3</CompositeItem>
 *     <CompositeItem>Item 4</CompositeItem>
 *   </CompositeOverflow>
 * </Composite>
 * ```
 */
export declare const CompositeOverflowDisclosure: import("../utils/types.js").Component<CompositeOverflowDisclosureOptions<"button">>;
export interface CompositeOverflowDisclosureOptions<T extends As = "button"> extends Omit<CompositeItemOptions<T>, "store">, PopoverDisclosureOptions<T> {
    /**
     * Object returned by the `useCompositeOverflowStore` hook.
     */
    store: CompositeOverflowStore;
}
export type CompositeOverflowDisclosureProps<T extends As = "button"> = Props<CompositeOverflowDisclosureOptions<T>>;
