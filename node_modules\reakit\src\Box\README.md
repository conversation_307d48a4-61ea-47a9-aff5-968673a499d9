---
path: /docs/box/
redirect_from:
  - /components/box/
  - /components/block/
  - /components/flex/
  - /components/grid/
  - /components/inline/
  - /components/inlineblock/
  - /components/inlineflex/
  - /components/avatar/
  - /components/blockquote/
  - /components/card/
  - /components/card/cardfit/
  - /components/code/
  - /components/heading/
  - /components/image/
  - /components/link/
  - /components/list/
  - /components/navigation/
  - /components/paragraph/
  - /components/table/
  - /components/table/tablewrapper/
---

# Box

`Box` is the most abstract component on top of which all other Reakit components are built. By default, it renders a `div` element.

<carbon-ad></carbon-ad>

## Installation

```sh
npm install reakit
```

Learn more in [Get started](/docs/get-started/).

## Usage

```jsx
import { Box } from "reakit/Box";

function Example() {
  return <Box>Box</Box>;
}
```

### `as` prop

Learn more about the `as` prop in [Composition](/docs/composition/#as-prop).

```jsx
import { Box } from "reakit/Box";

function Example() {
  return <Box as="button">Button</Box>;
}
```

### Render props

Learn more about render props in [Composition](/docs/composition/#render-props).

```jsx
import { Box } from "reakit/Box";

function Example() {
  return <Box>{(props) => <button {...props}>Button</button>}</Box>;
}
```

## Composition

- `Box` is used by all Reakit components.

Learn more in [Composition](/docs/composition/#props-hooks).

## Props

<!-- Automatically generated -->

### `Box`

No props to show
