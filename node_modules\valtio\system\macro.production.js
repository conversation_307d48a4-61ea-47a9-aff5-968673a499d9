System.register(["@babel/helper-module-imports","@babel/types","babel-plugin-macros"],function(h){"use strict";var i,a,l,c;return{setters:[function(e){i=e.addNamed},function(e){a=e},function(e){l=e.createMacro,c=e.MacroError}],execute:function(){var P=h("default",l(({references:g})=>{var s;(s=g.useProxy)==null||s.forEach(r=>{var u,d,v,m,p,f;const x=i(r,"useSnapshot","valtio"),n=(d=(u=r.parentPath)==null?void 0:u.get("arguments.0"))==null?void 0:d.node;if(!a.isIdentifier(n))throw new c("no proxy object");const b=a.identifier(`valtio_macro_snap_${n.name}`);(m=(v=r.parentPath)==null?void 0:v.parentPath)==null||m.replaceWith(a.variableDeclaration("const",[a.variableDeclarator(b,a.callExpression(x,[n]))]));let t=0;(f=(p=r.parentPath)==null?void 0:p.getFunctionParent())==null||f.traverse({Identifier(o){t===0&&o.node!==n&&o.node.name===n.name&&(o.node.name=b.name)},Function:{enter(){++t},exit(){--t}}})})},{configName:"valtio"}))}}});
