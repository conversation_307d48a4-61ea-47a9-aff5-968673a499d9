"use client";
import {
  createPopoverStore
} from "../__chunks/AF6IUUFN.js";
import "../__chunks/SX2XFD6A.js";
import "../__chunks/Z5IGYIPT.js";
import {
  createCompositeStore
} from "../__chunks/IERTEJ3A.js";
import "../__chunks/22K762VQ.js";
import {
  batch,
  createStore,
  setup,
  sync,
  throwOnConflictingProps
} from "../__chunks/EAHJFCU4.js";
import {
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import {
  isSafari,
  isTouchDevice
} from "../__chunks/MHPO2BXA.js";
import "../__chunks/DLOEKDPY.js";
import "../__chunks/7PRQYBBV.js";
import {
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/combobox/combobox-store.ts
var isSafariOnMobile = isSafari() && isTouchDevice();
function createComboboxStore(props = {}) {
  var _a;
  throwOnConflictingProps(props, props.store);
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const activeId = defaultValue(
    props.activeId,
    syncState == null ? void 0 : syncState.activeId,
    props.defaultActiveId,
    null
  );
  const composite = createCompositeStore(__spreadProps(__spreadValues({}, props), {
    activeId,
    includesBaseElement: defaultValue(
      props.includesBaseElement,
      syncState == null ? void 0 : syncState.includesBaseElement,
      true
    ),
    orientation: defaultValue(
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "vertical"
    ),
    focusLoop: defaultValue(props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true),
    focusWrap: defaultValue(props.focusWrap, syncState == null ? void 0 : syncState.focusWrap, true),
    virtualFocus: defaultValue(
      props.virtualFocus,
      syncState == null ? void 0 : syncState.virtualFocus,
      !isSafariOnMobile
    )
  }));
  const popover = createPopoverStore(__spreadProps(__spreadValues({}, props), {
    placement: defaultValue(
      props.placement,
      syncState == null ? void 0 : syncState.placement,
      "bottom-start"
    )
  }));
  const value = defaultValue(
    props.value,
    syncState == null ? void 0 : syncState.value,
    props.defaultValue,
    ""
  );
  const selectedValue = defaultValue(
    props.selectedValue,
    syncState == null ? void 0 : syncState.selectedValue,
    props.defaultSelectedValue,
    ""
  );
  const multiSelectable = Array.isArray(selectedValue);
  const initialState = __spreadProps(__spreadValues(__spreadValues({}, composite.getState()), popover.getState()), {
    value,
    selectedValue,
    resetValueOnSelect: defaultValue(
      props.resetValueOnSelect,
      syncState == null ? void 0 : syncState.resetValueOnSelect,
      multiSelectable
    ),
    resetValueOnHide: defaultValue(
      props.resetValueOnHide,
      syncState == null ? void 0 : syncState.resetValueOnHide,
      multiSelectable
    ),
    activeValue: syncState == null ? void 0 : syncState.activeValue
  });
  const combobox = createStore(initialState, composite, popover, props.store);
  setup(
    combobox,
    () => sync(combobox, ["resetValueOnHide", "mounted"], (state) => {
      if (!state.resetValueOnHide)
        return;
      if (state.mounted)
        return;
      combobox.setState("value", value);
    })
  );
  setup(
    combobox,
    () => sync(combobox, ["resetValueOnSelect", "selectedValue"], (state) => {
      if (!state.resetValueOnSelect)
        return;
      combobox.setState("value", value);
    })
  );
  setup(
    combobox,
    () => batch(combobox, ["mounted"], (state) => {
      if (state.mounted)
        return;
      combobox.setState("activeId", activeId);
      combobox.setState("moves", 0);
    })
  );
  setup(
    combobox,
    () => sync(combobox, ["moves", "activeId"], (state, prevState) => {
      if (state.moves === prevState.moves) {
        combobox.setState("activeValue", void 0);
      }
    })
  );
  setup(
    combobox,
    () => batch(combobox, ["moves", "renderedItems"], (state, prev) => {
      if (state.moves === prev.moves)
        return;
      const { activeId: activeId2 } = combobox.getState();
      const activeItem = composite.item(activeId2);
      combobox.setState("activeValue", activeItem == null ? void 0 : activeItem.value);
    })
  );
  return __spreadProps(__spreadValues(__spreadValues(__spreadValues({}, popover), composite), combobox), {
    setValue: (value2) => combobox.setState("value", value2),
    setSelectedValue: (selectedValue2) => combobox.setState("selectedValue", selectedValue2)
  });
}
export {
  createComboboxStore
};
