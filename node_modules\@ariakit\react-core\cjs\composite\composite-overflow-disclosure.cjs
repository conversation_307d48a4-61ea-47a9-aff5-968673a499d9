"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _LICANQTIcjs = require('../__chunks/LICANQTI.cjs');
require('../__chunks/QCG6J6X5.cjs');
require('../__chunks/LFHNPG2L.cjs');
require('../__chunks/3QW6ELTG.cjs');
require('../__chunks/5UTRYT6E.cjs');


var _IO6J4PANcjs = require('../__chunks/IO6J4PAN.cjs');
require('../__chunks/DAJUUBUI.cjs');
require('../__chunks/R5A2WTWB.cjs');
require('../__chunks/6ZZYUFPW.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/OLOZ5JT2.cjs');



var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/composite/composite-overflow-disclosure.ts
var _react = require('react');
var _store = require('@ariakit/core/utils/store');
var useCompositeOverflowDisclosure = _RNZNGEL4cjs.createHook.call(void 0, (_a) => {
  var _b = _a, { store } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store"]);
  const ref = _react.useRef.call(void 0, null);
  const [shouldRegisterItem, setShouldRegisterItem] = _react.useState.call(void 0, false);
  _react.useEffect.call(void 0, () => {
    return _store.sync.call(void 0, store, ["disclosureElement"], () => {
      store.setDisclosureElement(ref.current);
    });
  }, [store]);
  const onFocusProp = props.onFocus;
  const onFocus = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
    onFocusProp == null ? void 0 : onFocusProp(event);
    if (event.defaultPrevented)
      return;
    setShouldRegisterItem(true);
  });
  const onBlurProp = props.onBlur;
  const onBlur = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
    onBlurProp == null ? void 0 : onBlurProp(event);
    if (event.defaultPrevented)
      return;
    setShouldRegisterItem(false);
  });
  props = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
    "aria-hidden": !shouldRegisterItem
  }, props), {
    ref: _EO6LS72Hcjs.useMergeRefs.call(void 0, props.ref, ref),
    onFocus,
    onBlur
  });
  props = _IO6J4PANcjs.useCompositeItem.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), { shouldRegisterItem }));
  props = _LICANQTIcjs.usePopoverDisclosure.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, { store }, props));
  return props;
});
var CompositeOverflowDisclosure = _RNZNGEL4cjs.createComponent.call(void 0, (props) => {
  const htmlProps = useCompositeOverflowDisclosure(props);
  return _RNZNGEL4cjs.createElement.call(void 0, "button", htmlProps);
});
if (process.env.NODE_ENV !== "production") {
  CompositeOverflowDisclosure.displayName = "CompositeOverflowDisclosure";
}



exports.CompositeOverflowDisclosure = CompositeOverflowDisclosure; exports.useCompositeOverflowDisclosure = useCompositeOverflowDisclosure;
