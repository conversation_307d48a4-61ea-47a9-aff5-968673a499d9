import type { Store, StoreOptions, StoreProps } from "../utils/store.js";
import type { BivariantCallback } from "../utils/types.js";
/**
 * Creates a collection store.
 */
export declare function createCollectionStore<T extends CollectionStoreItem = CollectionStoreItem>(props?: CollectionStoreProps<T>): CollectionStore<T>;
export interface CollectionStoreItem {
    /**
     * The id of the item.
     */
    id: string;
    /**
     * The item HTML element. This is automatically set when the item is rendered.
     */
    element?: HTMLElement | null;
}
export interface CollectionStoreState<T extends CollectionStoreItem = CollectionStoreItem> {
    /**
     * Lists all items along with their metadata. This state is automatically
     * updated when an item is registered or unregistered using the
     * [`registerItem`](https://ariakit.org/reference/use-collection-store#registeritem)
     * function.
     */
    items: T[];
    /**
     * Lists all items, along with their metadata, in the exact order they appear in
     * the DOM. This state is automatically updated when an item is rendered or
     * unmounted using the
     * [`renderItem`](https://ariakit.org/reference/use-collection-store#renderitem)
     * function.
     */
    renderedItems: T[];
}
export interface CollectionStoreFunctions<T extends CollectionStoreItem = CollectionStoreItem> {
    /**
     * Registers an item in the collection. This function returns a cleanup
     * function that unregisters the item.
     * @example
     * const unregisterItem = store.registerItem({ id: "item-1" });
     * // on cleanup
     * unregisterItem();
     */
    registerItem: BivariantCallback<(item: T) => () => void>;
    /**
     * Renders an item in the collection. This function returns a cleanup function
     * that unmounts the item.
     * @example
     * const unrenderItem = store.renderItem({ id: "item-1" });
     * // on cleanup
     * unrenderItem();
     */
    renderItem: BivariantCallback<(item: T) => () => void>;
    /**
     * Gets an item by its id.
     * @example
     * const item = store.item("item-1");
     */
    item: (id: string | null | undefined) => T | null;
}
export interface CollectionStoreOptions<T extends CollectionStoreItem = CollectionStoreItem> extends StoreOptions<CollectionStoreState<T>, "items"> {
    /**
     * The defaut value for the
     * [`items`](https://ariakit.org/reference/collection-provider#items) state.
     * @default []
     */
    defaultItems?: CollectionStoreState<T>["items"];
}
export interface CollectionStoreProps<T extends CollectionStoreItem = CollectionStoreItem> extends CollectionStoreOptions<T>, StoreProps<CollectionStoreState<T>> {
}
export interface CollectionStore<T extends CollectionStoreItem = CollectionStoreItem> extends CollectionStoreFunctions<T>, Store<CollectionStoreState<T>> {
}
