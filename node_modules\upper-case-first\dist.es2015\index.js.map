{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,KAAa;IAC1C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC", "sourcesContent": ["/**\n * Upper case the first character of an input string.\n */\nexport function upperCaseFirst(input: string) {\n  return input.charAt(0).toUpperCase() + input.substr(1);\n}\n"]}