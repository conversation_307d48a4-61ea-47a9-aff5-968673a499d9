/*
 Copyright 2022 Google LLC
 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     https://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/

export {
  /**
   * @deprecated The "base+polyfill" build is deprecated.
   * See: https://bit.ly/3aqzsGm
   */
  onCLS,
} from './dist/modules/index.js';

export {
  /**
   * @deprecated The "base+polyfill" build is deprecated.
   * See: https://bit.ly/3aqzsGm
   */
  onFCP,
} from './dist/modules/index.js';

export {
  /**
   * @deprecated The "base+polyfill" build is deprecated.
   * See: https://bit.ly/3aqzsGm
   */
  onFID,
} from './dist/modules/index.js';

export {
  /**
   * @deprecated The "base+polyfill" build is deprecated.
   * See: https://bit.ly/3aqzsGm
   */
  onINP,
} from './dist/modules/index.js';

export {
  /**
   * @deprecated The "base+polyfill" build is deprecated.
   * See: https://bit.ly/3aqzsGm
   */
  onLCP,
} from './dist/modules/index.js';

export {
  /**
   * @deprecated The "base+polyfill" build is deprecated.
   * See: https://bit.ly/3aqzsGm
   */
  onTTFB,
} from './dist/modules/index.js';

export * from './dist/modules/deprecated.js';
export * from './dist/modules/types.js';
