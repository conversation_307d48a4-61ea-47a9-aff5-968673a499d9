"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _OUEE5HOScjs = require('../__chunks/OUEE5HOS.cjs');
require('../__chunks/ZL5DC555.cjs');
require('../__chunks/WZ3DRKGP.cjs');
require('../__chunks/KSPMHEYZ.cjs');
require('../__chunks/PZL34OVO.cjs');
require('../__chunks/344F3DYO.cjs');
require('../__chunks/KREY6HXD.cjs');
require('../__chunks/5ZOCN23X.cjs');
require('../__chunks/JVDUGICD.cjs');
require('../__chunks/VV6WA3I6.cjs');
require('../__chunks/7YLCVXZ7.cjs');
require('../__chunks/5GTNIPQ6.cjs');
require('../__chunks/7TN63K2T.cjs');
require('../__chunks/V24PR4PW.cjs');
require('../__chunks/6IUEXB4L.cjs');
require('../__chunks/JF225FQ5.cjs');
require('../__chunks/7566TIRW.cjs');
require('../__chunks/J3OG6T3B.cjs');
require('../__chunks/65LGW5LY.cjs');
require('../__chunks/XB3G2EO2.cjs');
require('../__chunks/CVD2AZE2.cjs');
require('../__chunks/2BIO7R5N.cjs');
require('../__chunks/LAUATD5O.cjs');
require('../__chunks/YPVQYY4J.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');
require('../__chunks/R66IWXK6.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/W5LJEMLB.cjs');
require('../__chunks/JAQJG42R.cjs');
require('../__chunks/OLOZ5JT2.cjs');


var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/composite/composite-overflow.ts
var hiddenStyle = {
  opacity: 0,
  pointerEvents: "none"
};
var useCompositeOverflow = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, {
      store,
      backdropProps: backdropPropsProp,
      wrapperProps: wrapperPropsProp,
      portal = false
    } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
      "store",
      "backdropProps",
      "wrapperProps",
      "portal"
    ]);
    const onFocusProp = props.onFocus;
    const onFocus = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onFocusProp == null ? void 0 : onFocusProp(event);
      if (event.defaultPrevented)
        return;
      store.show();
    });
    const mounted = store.useState("mounted");
    const getStyle = (styleProp) => mounted ? styleProp : _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, hiddenStyle), styleProp);
    const backdropProps = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      hidden: false
    }, backdropPropsProp), {
      style: getStyle(backdropPropsProp == null ? void 0 : backdropPropsProp.style)
    });
    const wrapperProps = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, wrapperPropsProp), {
      style: getStyle(wrapperPropsProp == null ? void 0 : wrapperPropsProp.style)
    });
    props = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      role: "presentation",
      hidden: false,
      focusable: false
    }, props), {
      onFocus
    });
    props = _OUEE5HOScjs.usePopover.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      store,
      backdropProps,
      wrapperProps,
      portal
    }, props));
    return props;
  }
);
var CompositeOverflow = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useCompositeOverflow(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "div", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  CompositeOverflow.displayName = "CompositeOverflow";
}



exports.CompositeOverflow = CompositeOverflow; exports.useCompositeOverflow = useCompositeOverflow;
