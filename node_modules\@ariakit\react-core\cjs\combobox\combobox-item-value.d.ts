import type { As, Options, Props } from "../utils/types.js";
import type { ComboboxStore } from "./combobox-store.js";
/**
 * Returns props to create a `ComboboxItemValue` component that displays a value
 * element inside a combobox item. The value will be split into span elements
 * and returned as the children prop. The portions of the value that correspond
 * to the store value will have a `data-user-value` attribute. The other
 * portions will have a `data-autocomplete-value` attribute.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore({ value: "p" });
 * const props = useComboboxItemValue({ store, value: "Apple" });
 * <Role {...props} />
 * // This will result in the following DOM:
 * <span>
 *   <span data-autocomplete-value>A</span>
 *   <span data-user-value>p</span>
 *   <span data-user-value>p</span>
 *   <span data-autocomplete-value>le</span>
 * </span>
 * ```
 */
export declare const useComboboxItemValue: import("../utils/types.js").Hook<ComboboxItemValueOptions<"span">>;
/**
 * Renders a value element inside a
 * [`ComboboxItem`](https://ariakit.org/reference/combobox-item). The value will
 * be split into span elements. The portions of the value that correspond to the
 * combobox value will have a
 * [`data-user-value`](https://ariakit.org/guide/styling#data-user-value)
 * attribute. The other portions will have a [`data-autocomplete-value`](https://ariakit.org/guide/styling#data-autocomplete-value)
 * attribute.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {5} "value"
 * <ComboboxProvider value="p">
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Apple">
 *       <ComboboxItemValue />
 *     </ComboboxItem>
 *   </ComboboxPopover>
 * </ComboboxProvider>
 *
 * // The Apple item will have a value element that looks like this:
 * <span>
 *   <span data-autocomplete-value>A</span>
 *   <span data-user-value>p</span>
 *   <span data-user-value>p</span>
 *   <span data-autocomplete-value>le</span>
 * </span>
 * ```
 */
export declare const ComboboxItemValue: import("../utils/types.js").Component<ComboboxItemValueOptions<"span">>;
export interface ComboboxItemValueOptions<T extends As = "span"> extends Options<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxList`](https://ariakit.org/reference/combobox-list) or
     * [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components' context will be used.
     */
    store?: ComboboxStore;
    /**
     * The current combobox item value. If not provided, the parent
     * [`ComboboxItem`](https://ariakit.org/reference/combobox-item) component's
     * [`value`](https://ariakit.org/reference/combobox-item#value) prop will be
     * used.
     */
    value?: string;
}
export type ComboboxItemValueProps<T extends As = "span"> = Props<ComboboxItemValueOptions<T>>;
