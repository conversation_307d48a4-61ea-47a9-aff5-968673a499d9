System.register(["valtio/vanilla"],function(k){"use strict";var S,y,b,x,C;return{setters:[function(v){S=v.subscribe,y=v.snapshot,b=v.proxy,x=v.getVersion,C=v.ref}],execute:function(){k({addComputed:R,derive:D,devtools:H,proxyMap:X,proxySet:V,proxyWithComputed:U,proxyWithHistory:K,subscribeKey:v,underive:W,watch:J});function v(n,t,e,s){return S(n,r=>{r.some(o=>o[1][0]===t)&&e(n[t])},s)}let m;function J(n,t){let e=!0;const s=new Set,r=new Map,o=()=>{e&&(e=!1,s.forEach(i=>i()),s.clear(),r.forEach(i=>i()),r.clear())},l=()=>{if(!e)return;s.forEach(a=>a()),s.clear();const i=new Set,u=m;m=s;try{const a=n(d=>(i.add(d),d));a&&s.add(a)}finally{m=u}r.forEach((a,d)=>{i.has(d)?i.delete(d):(r.delete(d),a())}),i.forEach(a=>{const d=S(a,l,t==null?void 0:t.sync);r.set(a,d)})};return m&&m.add(o),l(),o}const j=Symbol();function H(n,t){typeof t=="string"&&(console.warn("string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400"),t={name:t});const{enabled:e,name:s=""}=t||{};let r;try{r=(e!=null?e:!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!r)return;let o=!1;const l=r.connect({name:s}),i=S(n,a=>{const d=a.filter(([p,c])=>c[0]!==j).map(([p,c])=>`${p}:${c.map(String).join(".")}`).join(", ");if(d)if(o)o=!1;else{const p=Object.assign({},y(n));delete p[j],l.send({type:d,updatedAt:new Date().toLocaleString()},p)}}),u=l.subscribe(a=>{var d,p,c,h,E,w;if(a.type==="ACTION"&&a.payload)try{Object.assign(n,JSON.parse(a.payload))}catch(f){console.error(`please dispatch a serializable value that JSON.parse() and proxy() support
`,f)}if(a.type==="DISPATCH"&&a.state){if(((d=a.payload)==null?void 0:d.type)==="JUMP_TO_ACTION"||((p=a.payload)==null?void 0:p.type)==="JUMP_TO_STATE"){o=!0;const f=JSON.parse(a.state);Object.assign(n,f)}n[j]=a}else if(a.type==="DISPATCH"&&((c=a.payload)==null?void 0:c.type)==="COMMIT")l.init(y(n));else if(a.type==="DISPATCH"&&((h=a.payload)==null?void 0:h.type)==="IMPORT_STATE"){const f=(E=a.payload.nextLiftedState)==null?void 0:E.actionsById,$=((w=a.payload.nextLiftedState)==null?void 0:w.computedStates)||[];o=!0,$.forEach(({state:B},I)=>{const F=f[I]||"No action found";Object.assign(n,B),I===0?l.init(y(n)):l.send(F,y(n))})}});return l.init(y(n)),()=>{i(),u==null||u()}}const g=new WeakMap,O=new WeakMap,M=(n,t)=>{const e=g.get(n);e&&(e[0].forEach(s=>{const{d:r}=s;n!==r&&M(r)}),++e[2],t&&e[3].add(t))},z=(n,t)=>{const e=g.get(n);return e!=null&&e[2]?(e[3].add(t),!0):!1},T=n=>{const t=g.get(n);t&&(--t[2],t[2]||(t[3].forEach(e=>e()),t[3].clear()),t[0].forEach(e=>{const{d:s}=e;n!==s&&T(s)}))},_=n=>{const{s:t,d:e}=n;let s=O.get(e);s||(s=[new Set],O.set(n.d,s)),s[0].add(n);let r=g.get(t);if(!r){const o=new Set,l=S(t,i=>{o.forEach(u=>{const{d:a,c:d,n:p,i:c}=u;t===a&&i.every(h=>h[1].length===1&&c.includes(h[1][0]))||u.p||(M(t,d),p?T(t):u.p=Promise.resolve().then(()=>{delete u.p,T(t)}))})},!0);r=[o,l,0,new Set],g.set(t,r)}r[0].add(n)},A=n=>{const{s:t,d:e}=n,s=O.get(e);s==null||s[0].delete(n),(s==null?void 0:s[0].size)===0&&O.delete(e);const r=g.get(t);if(r){const[o,l]=r;o.delete(n),o.size||(l(),g.delete(t))}},N=n=>{const t=O.get(n);return t?Array.from(t[0]):[]},q=k("unstable_deriveSubscriptions",{add:_,remove:A,list:N});function D(n,t){const e=(t==null?void 0:t.proxy)||b({}),s=!!(t!=null&&t.sync),r=Object.keys(n);return r.forEach(o=>{if(Object.getOwnPropertyDescriptor(e,o))throw new Error("object property already defined");const l=n[o];let i=null;const u=()=>{if(i&&(Array.from(i).map(([c])=>z(c,u)).some(c=>c)||Array.from(i).every(([c,h])=>x(c)===h.v)))return;const a=new Map,d=l(c=>(a.set(c,{v:x(c)}),c)),p=()=>{a.forEach((c,h)=>{var E;const w=(E=i==null?void 0:i.get(h))==null?void 0:E.s;if(w)c.s=w;else{const f={s:h,d:e,k:o,c:u,n:s,i:r};_(f),c.s=f}}),i==null||i.forEach((c,h)=>{!a.has(h)&&c.s&&A(c.s)}),i=a};d instanceof Promise?d.finally(p):p(),e[o]=d};u()}),e}function W(n,t){const e=t!=null&&t.delete?new Set:null;N(n).forEach(s=>{const{k:r}=s;(!(t!=null&&t.keys)||t.keys.includes(r))&&(A(s),e&&e.add(r))}),e&&e.forEach(s=>{delete n[s]})}function R(n,t,e=n){console.warn("addComputed is deprecated. Please consider using `derive` or `proxyWithComputed` instead. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201");const s={};return Object.keys(t).forEach(r=>{s[r]=o=>t[r](o(n))}),D(s,{proxy:e})}function U(n,t){Object.keys(t).forEach(s=>{if(Object.getOwnPropertyDescriptor(n,s))throw new Error("object property already defined");const r=t[s],{get:o,set:l}=typeof r=="function"?{get:r}:r,i={};i.get=()=>o(y(e)),l&&(i.set=u=>l(e,u)),Object.defineProperty(n,s,i)});const e=b(n);return e}const L=n=>typeof n=="object"&&n!==null,P=n=>{if(!L(n))return n;const t=Array.isArray(n)?[]:Object.create(Object.getPrototypeOf(n));return Reflect.ownKeys(n).forEach(e=>{t[e]=P(n[e])}),t};function K(n,t=!1){const e=b({value:n,history:C({wip:void 0,snapshots:[],index:-1}),canUndo:()=>e.history.index>0,undo:()=>{e.canUndo()&&(e.value=e.history.wip=P(e.history.snapshots[--e.history.index]))},canRedo:()=>e.history.index<e.history.snapshots.length-1,redo:()=>{e.canRedo()&&(e.value=e.history.wip=P(e.history.snapshots[++e.history.index]))},saveHistory:()=>{e.history.snapshots.splice(e.history.index+1),e.history.snapshots.push(y(e).value),++e.history.index},subscribe:()=>S(e,s=>{s.every(r=>r[1][0]==="value"&&(r[0]!=="set"||r[2]!==e.history.wip))&&e.saveHistory()})});return e.saveHistory(),t||e.subscribe(),e}function V(n){const t=b({data:Array.from(new Set(n)),has(e){return this.data.indexOf(e)!==-1},add(e){let s=!1;return typeof e=="object"&&e!==null&&(s=this.data.indexOf(b(e))!==-1),this.data.indexOf(e)===-1&&!s&&this.data.push(e),this},delete(e){const s=this.data.indexOf(e);return s===-1?!1:(this.data.splice(s,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},forEach(e){this.data.forEach(s=>{e(s,s,this)})},get[Symbol.toStringTag](){return"Set"},toJSON(){return{}},[Symbol.iterator](){return this.data[Symbol.iterator]()},values(){return this.data.values()},keys(){return this.data.values()},entries(){return new Set(this.data).entries()}});return Object.defineProperties(t,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(t),t}function X(n){const t=b({data:Array.from(n||[]),has(e){return this.data.some(s=>s[0]===e)},set(e,s){const r=this.data.find(o=>o[0]===e);return r?r[1]=s:this.data.push([e,s]),this},get(e){var s;return(s=this.data.find(r=>r[0]===e))==null?void 0:s[1]},delete(e){const s=this.data.findIndex(r=>r[0]===e);return s===-1?!1:(this.data.splice(s,1),!0)},clear(){this.data.splice(0)},get size(){return this.data.length},toJSON(){return{}},forEach(e){this.data.forEach(s=>{e(s[1],s[0],this)})},keys(){return this.data.map(e=>e[0]).values()},values(){return this.data.map(e=>e[1]).values()},entries(){return new Map(this.data).entries()},get[Symbol.toStringTag](){return"Map"},[Symbol.iterator](){return this.entries()}});return Object.defineProperties(t,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(t),t}}}});
