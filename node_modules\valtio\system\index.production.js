System.register(["react","proxy-compare","use-sync-external-store/shim","valtio/vanilla"],function(a){"use strict";var r,i,o,f,b,p,y,h,c;return{setters:[function(n){r=n.useRef,i=n.useCallback,o=n.useEffect,f=n.useMemo},function(n){b=n.isChanged,p=n.createProxy},function(n){y=n.default},function(n){h=n.subscribe,c=n.snapshot,a({getVersion:n.getVersion,proxy:n.proxy,ref:n.ref,snapshot:n.snapshot,subscribe:n.subscribe,unstable_buildProxyFunction:n.unstable_buildProxyFunction})}],execute:function(){a("useSnapshot",S);const{useSyncExternalStore:n}=y;function S(t,x){const l=x==null?void 0:x.sync,s=r(),u=r();let g=!0;const k=n(i(e=>{const C=h(t,e,l);return e(),C},[t,l]),()=>{const e=c(t);try{if(!g&&s.current&&u.current&&!b(s.current,e,u.current,new WeakMap))return s.current}catch{}return e},()=>c(t));g=!1;const M=new WeakMap;o(()=>{s.current=k,u.current=M});const v=f(()=>new WeakMap,[]);return p(k,M,v)}}}});
