/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export { onCLS, CLSThresholds } from './onCLS.js';
export { onFCP, FCPThresholds } from './onFCP.js';
export { onFID, FIDThresholds } from './onFID.js';
export { onINP, INPThresholds } from './onINP.js';
export { onLCP, LCPThresholds } from './onLCP.js';
export { onTTFB, TTFBThresholds } from './onTTFB.js';
export * from './deprecated.js';
export * from './types.js';
