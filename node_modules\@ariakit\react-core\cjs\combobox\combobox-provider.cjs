"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _CFKRWKZRcjs = require('../__chunks/CFKRWKZR.cjs');


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/LUL2YWZ3.cjs');
require('../__chunks/CDNJPIEG.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/NRGWVSB5.cjs');
require('../__chunks/W5LJEMLB.cjs');
require('../__chunks/JAQJG42R.cjs');
require('../__chunks/OLOZ5JT2.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');
require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-provider.tsx
var _jsxruntime = require('react/jsx-runtime');
function ComboboxProvider(props = {}) {
  const store = _CFKRWKZRcjs.useComboboxStore.call(void 0, props);
  return /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _3WCBE6SUcjs.ComboboxContextProvider, { value: store, children: props.children });
}


exports.ComboboxProvider = ComboboxProvider;
