System.register(["proxy-compare"],function(D){"use strict";var A,M;return{setters:[function(d){A=d.markToTrack,M=d.getUntracked}],execute:function(){D({getVersion:_,proxy:V,ref:B,snapshot:z,subscribe:q});const d=o=>typeof o=="object"&&o!==null,k=new WeakSet,E=Symbol(),a=Symbol(),m=Symbol(),F=(o=Object.is,j=(t,f)=>new Proxy(t,f),P=t=>d(t)&&!k.has(t)&&(Array.isArray(t)||!(Symbol.iterator in t))&&!(t instanceof WeakMap)&&!(t instanceof WeakSet)&&!(t instanceof Error)&&!(t instanceof Number)&&!(t instanceof Date)&&!(t instanceof String)&&!(t instanceof RegExp)&&!(t instanceof ArrayBuffer),u=Symbol(),g=Symbol(),O=new WeakMap,R=(t,f,b)=>{const w=O.get(b);if((w==null?void 0:w[0])===t)return w[1];const s=Array.isArray(f)?[]:Object.create(Object.getPrototypeOf(f));return A(s,!0),O.set(b,[t,s]),Reflect.ownKeys(f).forEach(i=>{const c=Reflect.get(f,i,b);if(k.has(c))A(c,!1),s[i]=c;else if(c instanceof Promise)if(u in c)s[i]=c[u];else{const S=c[g]||c;Object.defineProperty(s,i,{get(){if(u in c)return c[u];throw S}})}else c!=null&&c[a]?s[i]=c[m]:s[i]=c}),Object.freeze(s)},T=new WeakMap,x=[1],I=t=>{if(!d(t))throw new Error("object required");const f=T.get(t);if(f)return f;let b=x[0];const w=new Set,s=(n,e=++x[0])=>{b!==e&&(b=e,w.forEach(r=>r(n,e)))},i=new Map,c=n=>{let e=i.get(n);return e||(e=(r,p)=>{const y=[...r];y[1]=[n,...y[1]],s(y,p)},i.set(n,e)),e},S=n=>{const e=i.get(n);return i.delete(n),e},K=Array.isArray(t)?[]:Object.create(Object.getPrototypeOf(t)),W=j(K,{get(n,e,r){return e===E?b:e===a?w:e===m?R(b,n,r):Reflect.get(n,e,r)},deleteProperty(n,e){const r=Reflect.get(n,e),p=r==null?void 0:r[a];p&&p.delete(S(e));const y=Reflect.deleteProperty(n,e);return y&&s(["delete",[e],r]),y},set(n,e,r,p){var y;const C=Reflect.has(n,e),v=Reflect.get(n,e,p);if(C&&o(v,r))return!0;const N=v==null?void 0:v[a];N&&N.delete(S(e)),d(r)&&(r=M(r)||r);let l;return(y=Object.getOwnPropertyDescriptor(n,e))!=null&&y.set?l=r:r instanceof Promise?l=r.then(h=>(l[u]=h,s(["resolve",[e],h]),h)).catch(h=>{l[g]=h,s(["reject",[e],h])}):r!=null&&r[a]?(l=r,l[a].add(c(e))):P(r)?(l=V(r),l[a].add(c(e))):l=r,Reflect.set(n,e,l,p),s(["set",[e],r,v]),!0}});return T.set(t,W),Reflect.ownKeys(t).forEach(n=>{const e=Object.getOwnPropertyDescriptor(t,n);e.get||e.set?Object.defineProperty(K,n,e):W[n]=t[n]}),W})=>[I,k,E,a,m,o,j,P,u,g,O,R,T,x],[U]=F();function V(o={}){return U(o)}function _(o){return d(o)?o[E]:void 0}function q(o,j,P){let u;const g=[],O=R=>{if(g.push(R),P){j(g.splice(0));return}u||(u=Promise.resolve().then(()=>{u=void 0,j(g.splice(0))}))};return o[a].add(O),()=>{o[a].delete(O)}}function z(o){return o[m]}function B(o){return k.add(o),o}const G=D("unstable_buildProxyFunction",F)}}});
