import type { DialogDisclosureOptions } from "../dialog/dialog-disclosure.js";
import type { As, Props } from "../utils/types.js";
import type { ComboboxStore } from "./combobox-store.js";
/**
 * Returns props to create a `ComboboxDisclosure` component that toggles the
 * combobox popover visibility when clicked.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore();
 * const props = useComboboxDisclosure({ store });
 * <Combobox store={store} />
 * <Role {...props} />
 * <ComboboxPopover store={store}>
 *   <ComboboxItem value="Item 1" />
 *   <ComboboxItem value="Item 2" />
 *   <ComboboxItem value="Item 3" />
 * </ComboboxPopover>
 * ```
 */
export declare const useComboboxDisclosure: import("../utils/types.js").Hook<ComboboxDisclosureOptions<"button">>;
/**
 * Renders a combobox disclosure button that toggles the
 * [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover) element's
 * visibility when clicked.
 *
 * Although this button is not tabbable, it remains accessible to screen reader
 * users. On clicking, it automatically shifts focus to the
 * [`Combobox`](https://ariakit.org/reference/combobox) element.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {3}
 * <ComboboxProvider>
 *   <Combobox />
 *   <ComboboxDisclosure />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Apple" />
 *     <ComboboxItem value="Banana" />
 *     <ComboboxItem value="Orange" />
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const ComboboxDisclosure: import("../utils/types.js").Component<ComboboxDisclosureOptions<"button">>;
export interface ComboboxDisclosureOptions<T extends As = "button"> extends DialogDisclosureOptions<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxProvider`](https://ariakit.org/reference/combobox-provider)
     * component's context will be used.
     */
    store?: ComboboxStore;
}
export type ComboboxDisclosureProps<T extends As = "button"> = Props<ComboboxDisclosureOptions<T>>;
