import type { CompositeGroupLabelOptions } from "../composite/composite-group-label.js";
import type { As, Props } from "../utils/types.js";
import type { ComboboxStore } from "./combobox-store.js";
/**
 * Returns props to create a `ComboboxGroupLabel` component. This hook should be
 * used in a component that's wrapped with `ComboboxGroup` so the
 * `aria-labelledby` is correctly set on the combobox group element.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * // This component should be wrapped with ComboboxGroup
 * const props = useComboboxGroupLabel();
 * <Role {...props}>Label</Role>
 * ```
 */
export declare const useComboboxGroupLabel: import("../utils/types.js").Hook<ComboboxGroupLabelOptions<"div">>;
/**
 * Renders a label in a combobox group. This component should be wrapped with
 * [`ComboboxGroup`](https://ariakit.org/reference/combobox-group) so the
 * `aria-labelledby` is correctly set on the group element.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {5}
 * <ComboboxProvider>
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxGroup>
 *       <ComboboxGroupLabel>Fruits</ComboboxGroupLabel>
 *       <ComboboxItem value="Apple" />
 *       <ComboboxItem value="Banana" />
 *     </ComboboxGroup>
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const ComboboxGroupLabel: import("../utils/types.js").Component<ComboboxGroupLabelOptions<"div">>;
export interface ComboboxGroupLabelOptions<T extends As = "div"> extends CompositeGroupLabelOptions<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxList`](https://ariakit.org/reference/combobox-list) or
     * [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components' context will be used.
     */
    store?: ComboboxStore;
}
export type ComboboxGroupLabelProps<T extends As = "div"> = Props<ComboboxGroupLabelOptions<T>>;
