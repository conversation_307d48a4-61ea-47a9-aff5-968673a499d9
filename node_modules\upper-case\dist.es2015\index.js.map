{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAQA;;GAEG;AACH,IAAM,gBAAgB,GAA2B;IAC/C,EAAE,EAAE;QACF,MAAM,EAAE,WAAW;QACnB,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;SACZ;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,WAAW;QACnB,GAAG,EAAE;YACH,CAAC,EAAE,QAAQ;SACZ;KACF;IACD,EAAE,EAAE;QACF,MAAM,EAAE,8DAA8D;QACtE,GAAG,EAAE;YACH,EAAE,EAAE,QAAQ;YACZ,EAAE,EAAE,QAAQ;YACZ,EAAE,EAAE,QAAQ;YACZ,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,QAAQ;SACd;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,MAAc;IACzD,IAAM,IAAI,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACpD,IAAI,IAAI;QAAE,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAX,CAAW,CAAC,CAAC,CAAC;IACzE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,GAAW;IACnC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC;AAC3B,CAAC", "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /[\\u0069]/g,\n    map: {\n      i: \"\\u0130\",\n    },\n  },\n  az: {\n    regexp: /[\\u0069]/g,\n    map: {\n      i: \"\\u0130\",\n    },\n  },\n  lt: {\n    regexp: /[\\u0069\\u006A\\u012F]\\u0307|\\u0069\\u0307[\\u0300\\u0301\\u0303]/g,\n    map: {\n      i̇: \"\\u0049\",\n      j̇: \"\\u004A\",\n      į̇: \"\\u012E\",\n      i̇̀: \"\\u00CC\",\n      i̇́: \"\\u00CD\",\n      i̇̃: \"\\u0128\",\n    },\n  },\n};\n\n/**\n * Localized upper case.\n */\nexport function localeUpperCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return upperCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return upperCase(str);\n}\n\n/**\n * Upper case as a function.\n */\nexport function upperCase(str: string) {\n  return str.toUpperCase();\n}\n"]}