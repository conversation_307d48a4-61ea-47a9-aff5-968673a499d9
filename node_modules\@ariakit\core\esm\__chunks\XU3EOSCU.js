"use client";
import {
  createCompositeStore
} from "./IERTEJ3A.js";
import {
  createStore
} from "./EAHJFCU4.js";
import {
  defaultValue
} from "./Y3OOHFCN.js";
import {
  __spreadProps,
  __spreadValues
} from "./4R3V3JGP.js";

// src/menubar/menubar-store.ts
function createMenubarStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const composite = createCompositeStore(__spreadProps(__spreadValues({}, props), {
    orientation: defaultValue(
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "horizontal"
    ),
    focusLoop: defaultValue(props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
  const initialState = __spreadValues({}, composite.getState());
  const menubar = createStore(initialState, composite, props.store);
  return __spreadValues(__spreadValues({}, composite), menubar);
}

export {
  createMenubarStore
};
