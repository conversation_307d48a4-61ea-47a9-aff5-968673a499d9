"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _TQTGWD5Kcjs = require('../__chunks/TQTGWD5K.cjs');
require('../__chunks/E53JW5BD.cjs');
require('../__chunks/I3Y4EXEF.cjs');
require('../__chunks/OTVLDMN2.cjs');


var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/tooltip/tooltip-store.ts
function createTooltipStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const hovercard = _TQTGWD5Kcjs.createHovercardStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    placement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.placement,
      syncState == null ? void 0 : syncState.placement,
      "top"
    ),
    hideTimeout: _KBNYGXWIcjs.defaultValue.call(void 0, props.hideTimeout, syncState == null ? void 0 : syncState.hideTimeout, 0)
  }));
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, hovercard.getState()), {
    type: _KBNYGXWIcjs.defaultValue.call(void 0, props.type, syncState == null ? void 0 : syncState.type, "description"),
    skipTimeout: _KBNYGXWIcjs.defaultValue.call(void 0, props.skipTimeout, syncState == null ? void 0 : syncState.skipTimeout, 300)
  });
  const tooltip = _F6HPKLO2cjs.createStore.call(void 0, initialState, hovercard, props.store);
  return _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, hovercard), tooltip);
}


exports.createTooltipStore = createTooltipStore;
