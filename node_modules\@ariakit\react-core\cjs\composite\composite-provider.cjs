"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _LUL2YWZ3cjs = require('../__chunks/LUL2YWZ3.cjs');
require('../__chunks/CDNJPIEG.cjs');


var _UZNYSPKPcjs = require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/OLOZ5JT2.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');
require('../__chunks/AV6KTKLE.cjs');

// src/composite/composite-provider.tsx
var _jsxruntime = require('react/jsx-runtime');
function CompositeProvider(props = {}) {
  const store = _LUL2YWZ3cjs.useCompositeStore.call(void 0, props);
  return /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _UZNYSPKPcjs.CompositeContextProvider, { value: store, children: props.children });
}


exports.CompositeProvider = CompositeProvider;
