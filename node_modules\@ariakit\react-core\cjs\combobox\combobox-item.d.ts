import type { MouseEvent } from "react";
import type { BooleanOrCallback } from "@ariakit/core/utils/types";
import type { CompositeHoverOptions } from "../composite/composite-hover.js";
import type { CompositeItemOptions } from "../composite/composite-item.js";
import type { As, Props } from "../utils/types.js";
import type { ComboboxStore } from "./combobox-store.js";
/**
 * Returns props to create a `ComboboxItem` component.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore();
 * const props = useComboboxItem({ store, value: "value" });
 * <Role {...props} />
 * ```
 */
export declare const useComboboxItem: import("../utils/types.js").Hook<ComboboxItemOptions<"div">>;
/**
 * Renders a combobox item inside
 * [`ComboboxList`](https://ariakit.org/reference/combobox-list) or
 * [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
 * components.
 *
 * By default, the [`value`](https://ariakit.org/reference/combobox-item#value)
 * prop will be rendered as the children, but this can be overriden.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {4-6}
 * <ComboboxProvider>
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Apple" />
 *     <ComboboxItem value="Banana" />
 *     <ComboboxItem value="Orange" />
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const ComboboxItem: import("../utils/types.js").Component<ComboboxItemOptions<"div">>;
export interface ComboboxItemOptions<T extends As = "div"> extends CompositeItemOptions<T>, CompositeHoverOptions<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxList`](https://ariakit.org/reference/combobox-list) or
     * [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components' context will be used.
     *
     * Live examples:
     * - [Navigation Menubar](https://ariakit.org/examples/menubar-navigation)
     */
    store?: ComboboxStore;
    /**
     * The value of the item. This will be rendered as the children by default.
     * - If
     *   [`setValueOnClick`](https://ariakit.org/reference/combobox-item#setvalueonclick)
     *   is set to `true`, this will be the value of the combobox input when the
     *   user clicks on this item.
     * - If
     *   [`selectValueOnClick`](https://ariakit.org/reference/combobox-item#selectvalueonclick)
     *   is set to `true`, this will be the value of the
     *   [`selectedValue`](https://ariakit.org/reference/combobox-provider#selectedvalue)
     *   state.
     * - If the
     *   [`autoComplete`](https://ariakit.org/reference/combobox#autocomplete)
     *   prop on the [`Combobox`](https://ariakit.org/reference/combobox)
     *   component is set to `both` or `inline`, this will be the value of the
     *   combobox input when the combobox loses focus.
     *
     * Live examples:
     * - [Animated Combobox](https://ariakit.org/examples/combobox-animated)
     * - [ComboboxCancel](https://ariakit.org/examples/combobox-cancel)
     * - [ComboboxDisclosure](https://ariakit.org/examples/combobox-disclosure)
     * - [Combobox filtering](https://ariakit.org/examples/combobox-filtering)
     * - [ComboboxGroup](https://ariakit.org/examples/combobox-group)
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     */
    value?: string;
    /**
     * Whether to hide the combobox when this item is clicked. By default, the
     * combobox will be hidden when the user clicks on an item with a
     * [`value`](https://ariakit.org/reference/combobox-item#value) prop, unless
     * the combobox is
     * [multi-selectable](https://ariakit.org/examples/combobox-multiple).
     *
     * Live examples:
     * - [Combobox with links](https://ariakit.org/examples/combobox-links)
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     */
    hideOnClick?: BooleanOrCallback<MouseEvent<HTMLElement>>;
    /**
     * Whether to set the
     * [`selectedValue`](https://ariakit.org/reference/combobox-provider#selectedvalue)
     * state using this item's
     * [`value`](https://ariakit.org/reference/combobox-item#value) when the item
     * is clicked. If a callback is provided, it will only be invoked if the item
     * has a value.
     *
     * Live examples:
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     * @default true
     */
    selectValueOnClick?: BooleanOrCallback<MouseEvent<HTMLElement>>;
    /**
     * Whether to set the combobox
     * [`value`](https://ariakit.org/reference/combobox-provider#value) state
     * using this item's
     * [`value`](https://ariakit.org/reference/combobox-item#value) when the item
     * is clicked. The default is `true`, unless the combobox is
     * [multi-selectable](https://ariakit.org/examples/combobox-multiple).
     *
     * Live examples:
     * - [Menu with Combobox](https://ariakit.org/examples/menu-combobox)
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     */
    setValueOnClick?: BooleanOrCallback<MouseEvent<HTMLElement>>;
    /**
     * @default false
     */
    focusOnHover?: CompositeHoverOptions["focusOnHover"];
}
export type ComboboxItemProps<T extends As = "div"> = Props<ComboboxItemOptions<T>>;
