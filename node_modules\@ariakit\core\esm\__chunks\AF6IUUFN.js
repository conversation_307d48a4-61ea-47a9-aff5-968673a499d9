"use client";
import {
  createDialogStore
} from "./SX2XFD6A.js";
import {
  createStore,
  mergeStore,
  omit,
  throwOnConflictingProps
} from "./EAHJFCU4.js";
import {
  defaultValue
} from "./Y3OOHFCN.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "./4R3V3JGP.js";

// src/popover/popover-store.ts
function createPopoverStore(_a = {}) {
  var _b = _a, {
    popover: otherPopover
  } = _b, props = __objRest(_b, [
    "popover"
  ]);
  const store = mergeStore(
    props.store,
    omit(otherPopover, [
      "arrowElement",
      "anchorElement",
      "contentElement",
      "popoverElement",
      "disclosureElement"
    ])
  );
  throwOnConflictingProps(props, store);
  const syncState = store == null ? void 0 : store.getState();
  const dialog = createDialogStore(__spreadProps(__spreadValues({}, props), { store }));
  const placement = defaultValue(
    props.placement,
    syncState == null ? void 0 : syncState.placement,
    "bottom"
  );
  const initialState = __spreadProps(__spreadValues({}, dialog.getState()), {
    placement,
    currentPlacement: placement,
    anchorElement: defaultValue(syncState == null ? void 0 : syncState.anchorElement, null),
    popoverElement: defaultValue(syncState == null ? void 0 : syncState.popoverElement, null),
    arrowElement: defaultValue(syncState == null ? void 0 : syncState.arrowElement, null),
    rendered: Symbol("rendered")
  });
  const popover = createStore(initialState, dialog, store);
  return __spreadProps(__spreadValues(__spreadValues({}, dialog), popover), {
    setAnchorElement: (element) => popover.setState("anchorElement", element),
    setPopoverElement: (element) => popover.setState("popoverElement", element),
    setArrowElement: (element) => popover.setState("arrowElement", element),
    render: () => popover.setState("rendered", Symbol("rendered"))
  });
}

export {
  createPopoverStore
};
