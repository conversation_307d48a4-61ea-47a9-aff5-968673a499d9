"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _VPQQSYH7cjs = require('../__chunks/VPQQSYH7.cjs');


var _63UPRTFZcjs = require('../__chunks/63UPRTFZ.cjs');
require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/OLOZ5JT2.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');
require('../__chunks/AV6KTKLE.cjs');

// src/checkbox/checkbox-provider.tsx
var _jsxruntime = require('react/jsx-runtime');
function CheckboxProvider(props = {}) {
  const store = _VPQQSYH7cjs.useCheckboxStore.call(void 0, props);
  return /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _63UPRTFZcjs.CheckboxContextProvider, { value: store, children: props.children });
}


exports.CheckboxProvider = CheckboxProvider;
