"use client";
import {
  createPopoverStore
} from "./AF6IUUFN.js";
import {
  createStore
} from "./EAHJFCU4.js";
import {
  defaultValue
} from "./Y3OOHFCN.js";
import {
  __spreadProps,
  __spreadValues
} from "./4R3V3JGP.js";

// src/hovercard/hovercard-store.ts
function createHovercardStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const popover = createPopoverStore(__spreadProps(__spreadValues({}, props), {
    placement: defaultValue(
      props.placement,
      syncState == null ? void 0 : syncState.placement,
      "bottom"
    )
  }));
  const timeout = defaultValue(props.timeout, syncState == null ? void 0 : syncState.timeout, 500);
  const initialState = __spreadProps(__spreadValues({}, popover.getState()), {
    timeout,
    showTimeout: defaultValue(props.showTimeout, syncState == null ? void 0 : syncState.showTimeout),
    hideTimeout: defaultValue(props.hideTimeout, syncState == null ? void 0 : syncState.hideTimeout),
    autoFocusOnShow: defaultValue(syncState == null ? void 0 : syncState.autoFocusOnShow, false)
  });
  const hovercard = createStore(initialState, popover, props.store);
  return __spreadProps(__spreadValues(__spreadValues({}, popover), hovercard), {
    setAutoFocusOnShow: (value) => hovercard.setState("autoFocusOnShow", value)
  });
}

export {
  createHovercardStore
};
