{"version": 3, "file": "index.js", "sources": ["../node_modules/date-fns/toDate.mjs", "../node_modules/date-fns/constructFrom.mjs", "../node_modules/date-fns/addDays.mjs", "../node_modules/date-fns/addMonths.mjs", "../node_modules/date-fns/_lib/defaultOptions.mjs", "../node_modules/date-fns/startOfWeek.mjs", "../node_modules/date-fns/startOfDay.mjs", "../node_modules/date-fns/addWeeks.mjs", "../node_modules/date-fns/addYears.mjs", "../node_modules/date-fns/endOfMonth.mjs", "../node_modules/date-fns/eachDayOfInterval.mjs", "../node_modules/date-fns/eachMonthOfInterval.mjs", "../node_modules/date-fns/eachWeekOfInterval.mjs", "../node_modules/date-fns/startOfMonth.mjs", "../node_modules/date-fns/endOfWeek.mjs", "../node_modules/date-fns/getDaysInMonth.mjs", "../node_modules/date-fns/isAfter.mjs", "../node_modules/date-fns/isBefore.mjs", "../node_modules/date-fns/isEqual.mjs", "../node_modules/date-fns/setMonth.mjs", "../node_modules/date-fns/set.mjs", "../node_modules/date-fns/setYear.mjs", "../node_modules/date-fns/startOfToday.mjs", "../node_modules/date-fns/subMonths.mjs", "../node_modules/date-fns/subYears.mjs", "../src/use-lilius.ts"], "sourcesContent": ["/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param argument - The value to convert\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nexport function toDate(argument) {\n  const argStr = Object.prototype.toString.call(argument);\n\n  // Clone the date\n  if (\n    argument instanceof Date ||\n    (typeof argument === \"object\" && argStr === \"[object Date]\")\n  ) {\n    // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n    return new argument.constructor(+argument);\n  } else if (\n    typeof argument === \"number\" ||\n    argStr === \"[object Number]\" ||\n    typeof argument === \"string\" ||\n    argStr === \"[object String]\"\n  ) {\n    // TODO: Can we get rid of as?\n    return new Date(argument);\n  } else {\n    // TODO: Can we get rid of as?\n    return new Date(NaN);\n  }\n}\n\n// Fallback for modularized imports:\nexport default toDate;\n", "/**\n * @name constructFrom\n * @category Generic Helpers\n * @summary Constructs a date using the reference date and the value\n *\n * @description\n * The function constructs a new date using the constructor from the reference\n * date and the given value. It helps to build generic functions that accept\n * date extensions.\n *\n * It defaults to `Date` if the passed reference date is a number or a string.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The reference date to take constructor from\n * @param value - The value to create the date\n *\n * @returns Date initialized using the given date and value\n *\n * @example\n * import { constructFrom } from 'date-fns'\n *\n * // A function that clones a date preserving the original type\n * function cloneDate<DateType extends Date(date: DateType): DateType {\n *   return constructFrom(\n *     date, // Use contrustor from the given date\n *     date.getTime() // Use the date value to create a new date\n *   )\n * }\n */\nexport function constructFrom(date, value) {\n  if (date instanceof Date) {\n    return new date.constructor(value);\n  } else {\n    return new Date(value);\n  }\n}\n\n// Fallback for modularized imports:\nexport default constructFrom;\n", "import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount) {\n  const _date = toDate(date);\n  if (isNaN(amount)) return constructFrom(date, NaN);\n  if (!amount) {\n    // If 0 days, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;\n", "import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name addMonths\n * @category Month Helpers\n * @summary Add the specified number of months to the given date.\n *\n * @description\n * Add the specified number of months to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be added.\n *\n * @returns The new date with the months added\n *\n * @example\n * // Add 5 months to 1 September 2014:\n * const result = addMonths(new Date(2014, 8, 1), 5)\n * //=> Sun Feb 01 2015 00:00:00\n *\n * // Add one month to 30 January 2023:\n * const result = addMonths(new Date(2023, 0, 30), 1)\n * //=> Tue Feb 28 2023 00:00:00\n */\nexport function addMonths(date, amount) {\n  const _date = toDate(date);\n  if (isNaN(amount)) return constructFrom(date, NaN);\n  if (!amount) {\n    // If 0 months, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  const dayOfMonth = _date.getDate();\n\n  // The JS Date object supports date math by accepting out-of-bounds values for\n  // month, day, etc. For example, new Date(2020, 0, 0) returns 31 Dec 2019 and\n  // new Date(2020, 13, 1) returns 1 Feb 2021.  This is *almost* the behavior we\n  // want except that dates will wrap around the end of a month, meaning that\n  // new Date(2020, 13, 31) will return 3 Mar 2021 not 28 Feb 2021 as desired. So\n  // we'll default to the end of the desired month by adding 1 to the desired\n  // month and using a date of 0 to back up one day to the end of the desired\n  // month.\n  const endOfDesiredMonth = constructFrom(date, _date.getTime());\n  endOfDesiredMonth.setMonth(_date.getMonth() + amount + 1, 0);\n  const daysInMonth = endOfDesiredMonth.getDate();\n  if (dayOfMonth >= daysInMonth) {\n    // If we're already at the end of the month, then this is the correct date\n    // and we're done.\n    return endOfDesiredMonth;\n  } else {\n    // Otherwise, we now know that setting the original day-of-month value won't\n    // cause an overflow, so set the desired day-of-month. Note that we can't\n    // just set the date of `endOfDesiredMonth` because that object may have had\n    // its time changed in the unusual case where where a DST transition was on\n    // the last day of the month and its local time was in the hour skipped or\n    // repeated next to a DST transition.  So we use `date` instead which is\n    // guaranteed to still have the original time.\n    _date.setFullYear(\n      endOfDesiredMonth.getFullYear(),\n      endOfDesiredMonth.getMonth(),\n      dayOfMonth,\n    );\n    return _date;\n  }\n}\n\n// Fallback for modularized imports:\nexport default addMonths;\n", "let defaultOptions = {};\n\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\n\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}\n", "import { toDate } from \"./toDate.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\n\n/**\n * The {@link startOfWeek} function options.\n */\n\n/**\n * @name startOfWeek\n * @category Week Helpers\n * @summary Return the start of a week for the given date.\n *\n * @description\n * Return the start of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week\n *\n * @example\n * // The start of a week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // If the week starts on Monday, the start of the week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n\n  _date.setDate(_date.getDate() - diff);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeek;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name startOfDay\n * @category Day Helpers\n * @summary Return the start of a day for the given date.\n *\n * @description\n * Return the start of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of a day\n *\n * @example\n * // The start of a day for 2 September 2014 11:55:00:\n * const result = startOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 00:00:00\n */\nexport function startOfDay(date) {\n  const _date = toDate(date);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfDay;\n", "import { addDays } from \"./addDays.mjs\";\n\n/**\n * @name addWeeks\n * @category Week Helpers\n * @summary Add the specified number of weeks to the given date.\n *\n * @description\n * Add the specified number of week to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be added.\n *\n * @returns The new date with the weeks added\n *\n * @example\n * // Add 4 weeks to 1 September 2014:\n * const result = addWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Sep 29 2014 00:00:00\n */\nexport function addWeeks(date, amount) {\n  const days = amount * 7;\n  return addDays(date, days);\n}\n\n// Fallback for modularized imports:\nexport default addWeeks;\n", "import { addMonths } from \"./addMonths.mjs\";\n\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be added.\n *\n * @returns The new date with the years added\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport function addYears(date, amount) {\n  return addMonths(date, amount * 12);\n}\n\n// Fallback for modularized imports:\nexport default addYears;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name endOfMonth\n * @category Month Helpers\n * @summary Return the end of a month for the given date.\n *\n * @description\n * Return the end of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The end of a month\n *\n * @example\n * // The end of a month for 2 September 2014 11:55:00:\n * const result = endOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMonth;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachDayOfInterval} function options.\n */\n\n/**\n * @name eachDayOfInterval\n * @category Interval Helpers\n * @summary Return the array of dates within the specified time interval.\n *\n * @description\n * Return the array of dates within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of days from the day of the interval start to the day of the interval end\n *\n * @example\n * // Each day between 6 October 2014 and 10 October 2014:\n * const result = eachDayOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 9, 10)\n * })\n * //=> [\n * //   Mon Oct 06 2014 00:00:00,\n * //   Tue Oct 07 2014 00:00:00,\n * //   Wed Oct 08 2014 00:00:00,\n * //   Thu Oct 09 2014 00:00:00,\n * //   Fri Oct 10 2014 00:00:00\n * // ]\n */\nexport function eachDayOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  const currentDate = reversed ? endDate : startDate;\n  currentDate.setHours(0, 0, 0, 0);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setDate(currentDate.getDate() + step);\n    currentDate.setHours(0, 0, 0, 0);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachDayOfInterval;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachMonthOfInterval} function options.\n */\n\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval\n *\n * @returns The array with starts of months from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * const result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\nexport function eachMonthOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  const currentDate = reversed ? endDate : startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setDate(1);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setMonth(currentDate.getMonth() + step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMonthOfInterval;\n", "import { addWeeks } from \"./addWeeks.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const startDateWeek = reversed\n    ? startOfWeek(endDate, options)\n    : startOfWeek(startDate, options);\n  const endDateWeek = reversed\n    ? startOfWeek(startDate, options)\n    : startOfWeek(endDate, options);\n\n  // Some timezones switch DST at midnight, making start of day unreliable in these timezones, 3pm is a safe bet\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(toDate(currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name startOfMonth\n * @category Month Helpers\n * @summary Return the start of a month for the given date.\n *\n * @description\n * Return the start of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The start of a month\n *\n * @example\n * // The start of a month for 2 September 2014 11:55:00:\n * const result = startOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfMonth(date) {\n  const _date = toDate(date);\n  _date.setDate(1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfMonth;\n", "import { toDate } from \"./toDate.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\n\n/**\n * The {@link endOfWeek} function options.\n */\n\n/**\n * @name endOfWeek\n * @category Week Helpers\n * @summary Return the end of a week for the given date.\n *\n * @description\n * Return the end of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a week\n *\n * @example\n * // The end of a week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 23:59:59.999\n *\n * @example\n * // If the week starts on Monday, the end of the week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setDate(_date.getDate() + diff);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfWeek;\n", "import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The number of days in a month\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport function getDaysInMonth(date) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n  const monthIndex = _date.getMonth();\n  const lastDayOfMonth = constructFrom(date, 0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDaysInMonth;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date that should be after the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is after the second date\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport function isAfter(date, dateToCompare) {\n  const _date = toDate(date);\n  const _dateToCompare = toDate(dateToCompare);\n  return _date.getTime() > _dateToCompare.getTime();\n}\n\n// Fallback for modularized imports:\nexport default isAfter;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date that should be before the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is before the second date\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport function isBefore(date, dateToCompare) {\n  const _date = toDate(date);\n  const _dateToCompare = toDate(dateToCompare);\n  return +_date < +_dateToCompare;\n}\n\n// Fallback for modularized imports:\nexport default isBefore;\n", "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The dates are equal\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport function isEqual(leftDate, rightDate) {\n  const _dateLeft = toDate(leftDate);\n  const _dateRight = toDate(rightDate);\n  return +_dateLeft === +_dateRight;\n}\n\n// Fallback for modularized imports:\nexport default isEqual;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { getDaysInMonth } from \"./getDaysInMonth.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month) {\n  const _date = toDate(date);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n\n  const dateWithDesiredMonth = constructFrom(date, 0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(dateWithDesiredMonth);\n  // Set the last day of the new month\n  // if the original date was the last day of the longer month\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { setMonth } from \"./setMonth.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param values - The date values to be set\n *\n * @returns The new date with options set\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\n\nexport function set(date, values) {\n  let _date = toDate(date);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+_date)) {\n    return constructFrom(date, NaN);\n  }\n\n  if (values.year != null) {\n    _date.setFullYear(values.year);\n  }\n\n  if (values.month != null) {\n    _date = setMonth(_date, values.month);\n  }\n\n  if (values.date != null) {\n    _date.setDate(values.date);\n  }\n\n  if (values.hours != null) {\n    _date.setHours(values.hours);\n  }\n\n  if (values.minutes != null) {\n    _date.setMinutes(values.minutes);\n  }\n\n  if (values.seconds != null) {\n    _date.setSeconds(values.seconds);\n  }\n\n  if (values.milliseconds != null) {\n    _date.setMilliseconds(values.milliseconds);\n  }\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default set;\n", "import { constructFrom } from \"./constructFrom.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param year - The year of the new date\n *\n * @returns The new date with the year set\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\nexport function setYear(date, year) {\n  const _date = toDate(date);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+_date)) {\n    return constructFrom(date, NaN);\n  }\n\n  _date.setFullYear(year);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setYear;\n", "import { startOfDay } from \"./startOfDay.mjs\";\n\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * @returns The start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport function startOfToday() {\n  return startOfDay(Date.now());\n}\n\n// Fallback for modularized imports:\nexport default startOfToday;\n", "import { addMonths } from \"./addMonths.mjs\";\n\n/**\n * @name subMonths\n * @category Month Helpers\n * @summary Subtract the specified number of months from the given date.\n *\n * @description\n * Subtract the specified number of months from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be subtracted.\n *\n * @returns The new date with the months subtracted\n *\n * @example\n * // Subtract 5 months from 1 February 2015:\n * const result = subMonths(new Date(2015, 1, 1), 5)\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function subMonths(date, amount) {\n  return addMonths(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subMonths;\n", "import { addYears } from \"./addYears.mjs\";\n\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be subtracted.\n *\n * @returns The new date with the years subtracted\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport function subYears(date, amount) {\n  return addYears(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subYears;\n", "import {\n  addMonths,\n  addYears,\n  eachDayOfInterval,\n  eachMonthOfInterval,\n  eachWeekOfInterval,\n  endOfMonth,\n  endOfWeek,\n  isAfter,\n  isBefore,\n  isEqual,\n  set,\n  setMonth,\n  setYear,\n  startOfMonth,\n  startOfToday,\n  startOfWeek,\n  subMonths,\n  subYears,\n} from \"date-fns\";\nimport { useCallback, useMemo, useState } from \"react\";\n\nexport enum Month {\n  JANUARY,\n  FEBRUARY,\n  MARCH,\n  APRIL,\n  MAY,\n  JUNE,\n  JULY,\n  AUGUST,\n  SEPTEMBER,\n  OCTOBER,\n  NOVEMBER,\n  DECEMBER,\n}\n\nexport enum Day {\n  SUNDAY,\n  MONDAY,\n  TUESDAY,\n  WEDNESDAY,\n  THURSDAY,\n  FRIDAY,\n  SATURDAY,\n}\n\nexport interface Options {\n  /**\n   * What day a week starts on within the calendar matrix.\n   *\n   * @default Day.SUNDAY\n   */\n  weekStartsOn?: Day;\n\n  /**\n   * The initial viewing date.\n   *\n   * @default new Date()\n   */\n  viewing?: Date;\n\n  /**\n   * The initial date(s) selection.\n   *\n   * @default []\n   */\n  selected?: Date[];\n\n  /**\n   * The number of months in the calendar.\n   *\n   * @default 1\n   */\n  numberOfMonths?: number;\n}\n\nexport interface Returns {\n  /**\n   * Returns a copy of the given date with the time set to 00:00:00:00.\n   */\n  clearTime: (date: Date) => Date;\n\n  /**\n   * Returns whether or not a date is between 2 other dates (inclusive).\n   */\n  inRange: (date: Date, min: Date, max: Date) => boolean;\n\n  /**\n   * The date represented in the calendar matrix. Note that\n   * the month and year are the only parts used.\n   */\n  viewing: Date;\n\n  /**\n   * Set the date represented in the calendar matrix. Note that\n   * the month and year are the only parts used.\n   */\n  setViewing: React.Dispatch<React.SetStateAction<Date>>;\n\n  /**\n   * Set the viewing date to today.\n   */\n  viewToday: () => void;\n\n  /**\n   * Set the viewing date to the given month.\n   */\n  viewMonth: (month: Month) => void;\n\n  /**\n   * Set the viewing date to the month before the current.\n   */\n  viewPreviousMonth: () => void;\n\n  /**\n   * Set the viewing date to the month after the current.\n   */\n  viewNextMonth: () => void;\n\n  /**\n   * Set the viewing date to the given year.\n   */\n  viewYear: (year: number) => void;\n\n  /**\n   * Set the viewing date to the year before the current.\n   */\n  viewPreviousYear: () => void;\n\n  /**\n   * Set the viewing date to the year after the current.\n   */\n  viewNextYear: () => void;\n\n  /**\n   * The dates currently selected.\n   */\n  selected: Date[];\n\n  /**\n   * Override the currently selected dates.\n   */\n  setSelected: React.Dispatch<React.SetStateAction<Date[]>>;\n\n  /**\n   * Reset the selected dates to [].\n   */\n  clearSelected: () => void;\n\n  /**\n   * Determine whether or not a date has been selected.\n   */\n  isSelected: (date: Date) => boolean;\n\n  /**\n   * Select one or more dates.\n   */\n  select: (date: Date | Date[], replaceExisting?: boolean) => void;\n\n  /**\n   * Deselect one or more dates.\n   */\n  deselect: (date: Date | Date[]) => void;\n\n  /**\n   * Toggle the selection of a date.\n   */\n  toggle: (date: Date, replaceExisting?: boolean) => void;\n\n  /**\n   * Select a range of dates (inclusive).\n   */\n  selectRange: (start: Date, end: Date, replaceExisting?: boolean) => void;\n\n  /**\n   * Deselect a range of dates (inclusive).\n   */\n  deselectRange: (start: Date, end: Date) => void;\n\n  /**\n   * A matrix of days based on the current viewing date.\n   */\n  calendar: Date[][][];\n}\n\nconst inRange = (date: Date, min: Date, max: Date) =>\n  (isEqual(date, min) || isAfter(date, min)) && (isEqual(date, max) || isBefore(date, max));\n\nconst clearTime = (date: Date) => set(date, { hours: 0, minutes: 0, seconds: 0, milliseconds: 0 });\n\nexport const useLilius = ({\n  weekStartsOn = Day.SUNDAY,\n  viewing: initialViewing = new Date(),\n  selected: initialSelected = [],\n  numberOfMonths = 1,\n}: Options = {}): Returns => {\n  const [viewing, setViewing] = useState<Date>(initialViewing);\n\n  const viewToday = useCallback(() => setViewing(startOfToday()), [setViewing]);\n\n  const viewMonth = useCallback((month: Month) => setViewing((v) => setMonth(v, month)), []);\n\n  const viewPreviousMonth = useCallback(() => setViewing((v) => subMonths(v, 1)), []);\n\n  const viewNextMonth = useCallback(() => setViewing((v) => addMonths(v, 1)), []);\n\n  const viewYear = useCallback((year: number) => setViewing((v) => setYear(v, year)), []);\n\n  const viewPreviousYear = useCallback(() => setViewing((v) => subYears(v, 1)), []);\n\n  const viewNextYear = useCallback(() => setViewing((v) => addYears(v, 1)), []);\n\n  const [selected, setSelected] = useState<Date[]>(initialSelected.map(clearTime));\n\n  const clearSelected = () => setSelected([]);\n\n  const isSelected = useCallback((date: Date) => selected.findIndex((s) => isEqual(s, date)) > -1, [selected]);\n\n  const select = useCallback((date: Date | Date[], replaceExisting?: boolean) => {\n    if (replaceExisting) {\n      setSelected(Array.isArray(date) ? date : [date]);\n    } else {\n      setSelected((selectedItems) => selectedItems.concat(Array.isArray(date) ? date : [date]));\n    }\n  }, []);\n\n  const deselect = useCallback(\n    (date: Date | Date[]) =>\n      setSelected((selectedItems) =>\n        Array.isArray(date)\n          ? selectedItems.filter((s) => !date.map((d) => d.getTime()).includes(s.getTime()))\n          : selectedItems.filter((s) => !isEqual(s, date)),\n      ),\n    [],\n  );\n\n  const toggle = useCallback(\n    (date: Date, replaceExisting?: boolean) => (isSelected(date) ? deselect(date) : select(date, replaceExisting)),\n    [deselect, isSelected, select],\n  );\n\n  const selectRange = useCallback((start: Date, end: Date, replaceExisting?: boolean) => {\n    if (replaceExisting) {\n      setSelected(eachDayOfInterval({ start, end }));\n    } else {\n      setSelected((selectedItems) => selectedItems.concat(eachDayOfInterval({ start, end })));\n    }\n  }, []);\n\n  const deselectRange = useCallback((start: Date, end: Date) => {\n    setSelected((selectedItems) =>\n      selectedItems.filter(\n        (s) =>\n          !eachDayOfInterval({ start, end })\n            .map((d) => d.getTime())\n            .includes(s.getTime()),\n      ),\n    );\n  }, []);\n\n  const calendar = useMemo<Date[][][]>(\n    () =>\n      eachMonthOfInterval({\n        start: startOfMonth(viewing),\n        end: endOfMonth(addMonths(viewing, numberOfMonths - 1)),\n      }).map((month) =>\n        eachWeekOfInterval(\n          {\n            start: startOfMonth(month),\n            end: endOfMonth(month),\n          },\n          { weekStartsOn },\n        ).map((week) =>\n          eachDayOfInterval({\n            start: startOfWeek(week, { weekStartsOn }),\n            end: endOfWeek(week, { weekStartsOn }),\n          }),\n        ),\n      ),\n    [viewing, weekStartsOn, numberOfMonths],\n  );\n\n  return {\n    clearTime,\n    inRange,\n    viewing,\n    setViewing,\n    viewToday,\n    viewMonth,\n    viewPreviousMonth,\n    viewNextMonth,\n    viewYear,\n    viewPreviousYear,\n    viewNextYear,\n    selected,\n    setSelected,\n    clearSelected,\n    isSelected,\n    select,\n    deselect,\n    toggle,\n    selectRange,\n    deselectRange,\n    calendar,\n  };\n};\n"], "names": ["Month", "Day", "useState", "useCallback", "useMemo"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,QAAQ,EAAE;AACjC,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1D;AACA;AACA,EAAE;AACF,IAAI,QAAQ,YAAY,IAAI;AAC5B,KAAK,OAAO,QAAQ,KAAK,QAAQ,IAAI,MAAM,KAAK,eAAe,CAAC;AAChE,IAAI;AACJ;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC/C,GAAG,MAAM;AACT,IAAI,OAAO,QAAQ,KAAK,QAAQ;AAChC,IAAI,MAAM,KAAK,iBAAiB;AAChC,IAAI,OAAO,QAAQ,KAAK,QAAQ;AAChC,IAAI,MAAM,KAAK,iBAAiB;AAChC,IAAI;AACJ;AACA,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9B,GAAG,MAAM;AACT;AACA,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,GAAG;AACH;;ACtDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;AAC3C,EAAE,IAAI,IAAI,YAAY,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,GAAG,MAAM;AACT,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,GAAG;AACH;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AACtC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACrD,EAAE,IAAI,CAAC,MAAM,EAAE;AACf;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;AAC1C,EAAE,OAAO,KAAK,CAAC;AACf;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE;AACxC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACrD,EAAE,IAAI,CAAC,MAAM,EAAE;AACf;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AACjE,EAAE,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,EAAE,MAAM,WAAW,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC;AAClD,EAAE,IAAI,UAAU,IAAI,WAAW,EAAE;AACjC;AACA;AACA,IAAI,OAAO,iBAAiB,CAAC;AAC7B,GAAG,MAAM;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,WAAW;AACrB,MAAM,iBAAiB,CAAC,WAAW,EAAE;AACrC,MAAM,iBAAiB,CAAC,QAAQ,EAAE;AAClC,MAAM,UAAU;AAChB,KAAK,CAAC;AACN,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;;AClEA,IAAI,cAAc,GAAG,EAAE,CAAC;AACxB;AACO,SAAS,iBAAiB,GAAG;AACpC,EAAE,OAAO,cAAc,CAAC;AACxB;;ACDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AAC3C,EAAE,MAAM,cAAc,GAAG,iBAAiB,EAAE,CAAC;AAC7C,EAAE,MAAM,YAAY;AACpB,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY;AAC1C,IAAI,cAAc,CAAC,YAAY;AAC/B,IAAI,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY;AAChD,IAAI,CAAC,CAAC;AACN;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,YAAY,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC;AACjE;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACxC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,EAAE,OAAO,KAAK,CAAC;AACf;;AC/CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAAE;AACjC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,EAAE,OAAO,KAAK,CAAC;AACf;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE;AACvC,EAAE,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;AAC1B,EAAE,OAAO,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7B;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE;AACvC,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AACtC;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,IAAI,EAAE;AACjC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AACjC,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AAClC,EAAE,OAAO,KAAK,CAAC;AACf;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE;AACrD,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,EAAE,IAAI,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC;AACvC,EAAE,MAAM,OAAO,GAAG,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,WAAW,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;AACrD,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC;AACA,EAAE,IAAI,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC;AAChC,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;AACvB,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE;AAChB,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;AACjB,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH;AACA,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB;AACA,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,EAAE;AAClC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AACpC,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACtD,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC;AAC5C;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE;AACvD,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,EAAE,IAAI,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC;AACvC,EAAE,MAAM,OAAO,GAAG,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,WAAW,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;AACrD,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACzB;AACA,EAAE,IAAI,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC;AAChC,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;AACvB,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE;AAChB,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;AACjB,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH;AACA,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB;AACA,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,EAAE;AAClC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AACpC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;AACxD,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC;AAC5C;;ACzDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE;AACtD,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,EAAE,IAAI,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC;AACvC,EAAE,MAAM,aAAa,GAAG,QAAQ;AAChC,MAAM,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC;AACnC,MAAM,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACtC,EAAE,MAAM,WAAW,GAAG,QAAQ;AAC9B,MAAM,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC;AACrC,MAAM,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACpC;AACA;AACA,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC7B,EAAE,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC3B;AACA,EAAE,MAAM,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;AACzC,EAAE,IAAI,WAAW,GAAG,aAAa,CAAC;AAClC;AACA,EAAE,IAAI,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC;AAChC,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;AACvB,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE;AAChB,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;AACjB,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH;AACA,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB;AACA,EAAE,OAAO,CAAC,WAAW,IAAI,OAAO,EAAE;AAClC,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AACpC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC9C,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,GAAG,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC;AAC5C;;AC1EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,IAAI,EAAE;AACnC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACnB,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,EAAE,OAAO,KAAK,CAAC;AACf;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE;AACzC,EAAE,MAAM,cAAc,GAAG,iBAAiB,EAAE,CAAC;AAC7C,EAAE,MAAM,YAAY;AACpB,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY;AAC1C,IAAI,cAAc,CAAC,YAAY;AAC/B,IAAI,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY;AAChD,IAAI,CAAC,CAAC;AACN;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,CAAC;AACxE;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACxC,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AAClC,EAAE,OAAO,KAAK,CAAC;AACf;;AC9CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,IAAI,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;AACnC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AACtC,EAAE,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAChD,EAAE,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACtD,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,EAAE,OAAO,cAAc,CAAC,OAAO,EAAE,CAAC;AAClC;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAE,aAAa,EAAE;AAC7C,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAC/C,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;AACpD;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,aAAa,EAAE;AAC9C,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAC/C,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,cAAc,CAAC;AAClC;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE;AAC7C,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACvC,EAAE,OAAO,CAAC,SAAS,KAAK,CAAC,UAAU,CAAC;AACpC;;ACzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;AACtC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;AACnC,EAAE,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AAC9B;AACA,EAAE,MAAM,oBAAoB,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtD,EAAE,oBAAoB,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACpD,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,EAAE,MAAM,WAAW,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC;AAC3D;AACA;AACA,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC;AACpD,EAAE,OAAO,KAAK,CAAC;AACf;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE;AAClC,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B;AACA;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;AACrB,IAAI,OAAO,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;AAC3B,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;AAC5B,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;AAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;AAC5B,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;AAC9B,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;AAC9B,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,EAAE;AACnC,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC/C,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf;;ACvEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE;AACpC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA;AACA,EAAE,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;AACrB,IAAI,OAAO,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpC,GAAG;AACH;AACA,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,OAAO,KAAK,CAAC;AACf;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,GAAG;AAC/B,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE;AACxC,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;AAClC;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE;AACvC,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;AACjC;;ACFYA,uBAaX;AAbD,CAAA,UAAY,KAAK,EAAA;AACf,IAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG,CAAA;AACH,IAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAQ,CAAA;AACV,CAAC,EAbWA,aAAK,KAALA,aAAK,GAahB,EAAA,CAAA,CAAA,CAAA;AAEWC,qBAQX;AARD,CAAA,UAAY,GAAG,EAAA;AACb,IAAA,GAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,GAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,GAAA,CAAA,GAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,GAAA,CAAA,GAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACT,IAAA,GAAA,CAAA,GAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACR,IAAA,GAAA,CAAA,GAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACN,IAAA,GAAA,CAAA,GAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AACV,CAAC,EARWA,WAAG,KAAHA,WAAG,GAQd,EAAA,CAAA,CAAA,CAAA;AA6ID,IAAM,OAAO,GAAG,UAAC,IAAU,EAAE,GAAS,EAAE,GAAS,EAAA;AAC/C,IAAA,OAAA,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAA;AAAzF,CAAyF,CAAC;AAE5F,IAAM,SAAS,GAAG,UAAC,IAAU,EAAK,EAAA,OAAA,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC,CAAA,EAAA,CAAC;AAE5F,IAAM,SAAS,GAAG,UAAC,EAKX,EAAA;AALW,IAAA,IAAA,EAAA,GAAA,EAAA,KAAA,KAAA,CAAA,GAKb,EAAE,GAAA,EAAA,EAJb,EAAA,GAAA,EAAA,CAAA,YAAyB,EAAzB,YAAY,GAAA,EAAA,KAAA,KAAA,CAAA,GAAGA,WAAG,CAAC,MAAM,GAAA,EAAA,EACzB,EAAA,GAAA,EAAA,CAAA,OAAoC,EAA3B,cAAc,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,IAAI,IAAI,EAAE,GAAA,EAAA,EACpC,EAAA,GAAA,EAAA,CAAA,QAA8B,EAApB,eAAe,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAA,EAAA,EAC9B,EAAkB,GAAA,EAAA,CAAA,cAAA,EAAlB,cAAc,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,CAAC,GAAA,EAAA,CAAA;IAEZ,IAAA,EAAA,GAAwBC,cAAQ,CAAO,cAAc,CAAC,EAArD,OAAO,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,UAAU,GAAA,EAAA,CAAA,CAAA,CAAkC,CAAC;AAE7D,IAAA,IAAM,SAAS,GAAGC,iBAAW,CAAC,YAAM,EAAA,OAAA,UAAU,CAAC,YAAY,EAAE,CAAC,GAAA,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AAE9E,IAAA,IAAM,SAAS,GAAGA,iBAAW,CAAC,UAAC,KAAY,EAAK,EAAA,OAAA,UAAU,CAAC,UAAC,CAAC,EAAK,EAAA,OAAA,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAlB,EAAkB,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAC;IAE3F,IAAM,iBAAiB,GAAGA,iBAAW,CAAC,YAAA,EAAM,OAAA,UAAU,CAAC,UAAC,CAAC,EAAA,EAAK,OAAA,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAf,EAAe,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAC;IAEpF,IAAM,aAAa,GAAGA,iBAAW,CAAC,YAAA,EAAM,OAAA,UAAU,CAAC,UAAC,CAAC,EAAA,EAAK,OAAA,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAf,EAAe,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAC;AAEhF,IAAA,IAAM,QAAQ,GAAGA,iBAAW,CAAC,UAAC,IAAY,EAAK,EAAA,OAAA,UAAU,CAAC,UAAC,CAAC,EAAK,EAAA,OAAA,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAhB,EAAgB,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAC;IAExF,IAAM,gBAAgB,GAAGA,iBAAW,CAAC,YAAA,EAAM,OAAA,UAAU,CAAC,UAAC,CAAC,EAAA,EAAK,OAAA,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAd,EAAc,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAC;IAElF,IAAM,YAAY,GAAGA,iBAAW,CAAC,YAAA,EAAM,OAAA,UAAU,CAAC,UAAC,CAAC,EAAA,EAAK,OAAA,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAd,EAAc,CAAC,CAAA,EAAA,EAAE,EAAE,CAAC,CAAC;AAExE,IAAA,IAAA,KAA0BD,cAAQ,CAAS,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAzE,QAAQ,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,WAAW,QAAoD,CAAC;IAEjF,IAAM,aAAa,GAAG,YAAA,EAAM,OAAA,WAAW,CAAC,EAAE,CAAC,CAAf,EAAe,CAAC;AAE5C,IAAA,IAAM,UAAU,GAAGC,iBAAW,CAAC,UAAC,IAAU,EAAA,EAAK,OAAA,QAAQ,CAAC,SAAS,CAAC,UAAC,CAAC,IAAK,OAAA,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAhB,EAAgB,CAAC,GAAG,CAAC,CAAC,CAAA,EAAA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE7G,IAAA,IAAM,MAAM,GAAGA,iBAAW,CAAC,UAAC,IAAmB,EAAE,eAAyB,EAAA;AACxE,QAAA,IAAI,eAAe,EAAE;AACnB,YAAA,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAClD,SAAA;AAAM,aAAA;AACL,YAAA,WAAW,CAAC,UAAC,aAAa,EAAA,EAAK,OAAA,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAzD,EAAyD,CAAC,CAAC;AAC3F,SAAA;KACF,EAAE,EAAE,CAAC,CAAC;AAEP,IAAA,IAAM,QAAQ,GAAGA,iBAAW,CAC1B,UAAC,IAAmB,EAAA;QAClB,OAAA,WAAW,CAAC,UAAC,aAAa,EAAA;AACxB,YAAA,OAAA,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AACjB,kBAAE,aAAa,CAAC,MAAM,CAAC,UAAC,CAAC,EAAK,EAAA,OAAA,CAAC,IAAI,CAAC,GAAG,CAAC,UAAC,CAAC,EAAA,EAAK,OAAA,CAAC,CAAC,OAAO,EAAE,CAAX,EAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA,EAAA,CAAC;AAClF,kBAAE,aAAa,CAAC,MAAM,CAAC,UAAC,CAAC,EAAK,EAAA,OAAA,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,EAAA,CAAC,CAAA;AAFlD,SAEkD,CACnD,CAAA;KAAA,EACH,EAAE,CACH,CAAC;AAEF,IAAA,IAAM,MAAM,GAAGA,iBAAW,CACxB,UAAC,IAAU,EAAE,eAAyB,EAAA,EAAK,QAAC,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,eAAe,CAAC,EAAC,EAAA,EAC9G,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAC/B,CAAC;IAEF,IAAM,WAAW,GAAGA,iBAAW,CAAC,UAAC,KAAW,EAAE,GAAS,EAAE,eAAyB,EAAA;AAChF,QAAA,IAAI,eAAe,EAAE;AACnB,YAAA,WAAW,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAA,KAAA,EAAE,GAAG,EAAA,GAAA,EAAE,CAAC,CAAC,CAAC;AAChD,SAAA;AAAM,aAAA;YACL,WAAW,CAAC,UAAC,aAAa,EAAA,EAAK,OAAA,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAA,KAAA,EAAE,GAAG,EAAA,GAAA,EAAE,CAAC,CAAC,CAAA,EAAA,CAAC,CAAC;AACzF,SAAA;KACF,EAAE,EAAE,CAAC,CAAC;AAEP,IAAA,IAAM,aAAa,GAAGA,iBAAW,CAAC,UAAC,KAAW,EAAE,GAAS,EAAA;QACvD,WAAW,CAAC,UAAC,aAAa,EAAA;AACxB,YAAA,OAAA,aAAa,CAAC,MAAM,CAClB,UAAC,CAAC,EAAA;gBACA,OAAA,CAAC,iBAAiB,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,EAAA,GAAA,EAAE,CAAC;qBAC/B,GAAG,CAAC,UAAC,CAAC,EAAK,EAAA,OAAA,CAAC,CAAC,OAAO,EAAE,CAAX,EAAW,CAAC;AACvB,qBAAA,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;AAFxB,aAEwB,CAC3B,CAAA;AALD,SAKC,CACF,CAAC;KACH,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,QAAQ,GAAGC,aAAO,CACtB,YAAA;AACE,QAAA,OAAA,mBAAmB,CAAC;AAClB,YAAA,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC;YAC5B,GAAG,EAAE,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AACxD,SAAA,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,EAAA;AACX,YAAA,OAAA,kBAAkB,CAChB;AACE,gBAAA,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC;AAC1B,gBAAA,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC;aACvB,EACD,EAAE,YAAY,EAAA,YAAA,EAAE,CACjB,CAAC,GAAG,CAAC,UAAC,IAAI,EAAA;AACT,gBAAA,OAAA,iBAAiB,CAAC;oBAChB,KAAK,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,YAAY,EAAA,YAAA,EAAE,CAAC;oBAC1C,GAAG,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,YAAY,EAAA,YAAA,EAAE,CAAC;iBACvC,CAAC,CAAA;AAHF,aAGE,CACH,CAAA;AAXD,SAWC,CACF,CAAA;KAAA,EACH,CAAC,OAAO,EAAE,YAAY,EAAE,cAAc,CAAC,CACxC,CAAC;IAEF,OAAO;AACL,QAAA,SAAS,EAAA,SAAA;AACT,QAAA,OAAO,EAAA,OAAA;AACP,QAAA,OAAO,EAAA,OAAA;AACP,QAAA,UAAU,EAAA,UAAA;AACV,QAAA,SAAS,EAAA,SAAA;AACT,QAAA,SAAS,EAAA,SAAA;AACT,QAAA,iBAAiB,EAAA,iBAAA;AACjB,QAAA,aAAa,EAAA,aAAA;AACb,QAAA,QAAQ,EAAA,QAAA;AACR,QAAA,gBAAgB,EAAA,gBAAA;AAChB,QAAA,YAAY,EAAA,YAAA;AACZ,QAAA,QAAQ,EAAA,QAAA;AACR,QAAA,WAAW,EAAA,WAAA;AACX,QAAA,aAAa,EAAA,aAAA;AACb,QAAA,UAAU,EAAA,UAAA;AACV,QAAA,MAAM,EAAA,MAAA;AACN,QAAA,QAAQ,EAAA,QAAA;AACR,QAAA,MAAM,EAAA,MAAA;AACN,QAAA,WAAW,EAAA,WAAA;AACX,QAAA,aAAa,EAAA,aAAA;AACb,QAAA,QAAQ,EAAA,QAAA;KACT,CAAC;AACJ;;;;"}