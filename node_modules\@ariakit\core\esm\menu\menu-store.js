"use client";
import {
  createHovercardStore
} from "../__chunks/SOLWE6E5.js";
import "../__chunks/AF6IUUFN.js";
import "../__chunks/SX2XFD6A.js";
import "../__chunks/Z5IGYIPT.js";
import {
  createCompositeStore
} from "../__chunks/IERTEJ3A.js";
import "../__chunks/22K762VQ.js";
import {
  createStore,
  mergeStore,
  omit,
  pick,
  setup,
  sync,
  throwOnConflictingProps
} from "../__chunks/EAHJFCU4.js";
import {
  applyState,
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import "../__chunks/DLOEKDPY.js";
import "../__chunks/7PRQYBBV.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/menu/menu-store.ts
function createMenuStore(_a = {}) {
  var _b = _a, {
    combobox,
    parent,
    menubar
  } = _b, props = __objRest(_b, [
    "combobox",
    "parent",
    "menubar"
  ]);
  const parentIsMenubar = !!menubar && !parent;
  const store = mergeStore(
    props.store,
    pick(parent, ["values"]),
    omit(combobox, [
      "arrowElement",
      "anchorElement",
      "contentElement",
      "popoverElement",
      "disclosureElement"
    ])
  );
  throwOnConflictingProps(props, store);
  const syncState = store.getState();
  const composite = createCompositeStore(__spreadProps(__spreadValues({}, props), {
    store,
    orientation: defaultValue(
      props.orientation,
      syncState.orientation,
      "vertical"
    )
  }));
  const hovercard = createHovercardStore(__spreadProps(__spreadValues({}, props), {
    store,
    placement: defaultValue(
      props.placement,
      syncState.placement,
      "bottom-start"
    ),
    timeout: defaultValue(
      props.timeout,
      syncState.timeout,
      parentIsMenubar ? 0 : 150
    ),
    hideTimeout: defaultValue(props.hideTimeout, syncState.hideTimeout, 0)
  }));
  const initialState = __spreadProps(__spreadValues(__spreadValues({}, composite.getState()), hovercard.getState()), {
    initialFocus: defaultValue(syncState.initialFocus, "container"),
    values: defaultValue(
      props.values,
      syncState.values,
      props.defaultValues,
      {}
    )
  });
  const menu = createStore(initialState, composite, hovercard, store);
  setup(
    menu,
    () => sync(menu, ["mounted"], (state) => {
      if (state.mounted)
        return;
      menu.setState("activeId", null);
    })
  );
  setup(
    menu,
    () => sync(parent, ["orientation"], (state) => {
      menu.setState(
        "placement",
        state.orientation === "vertical" ? "right-start" : "bottom-start"
      );
    })
  );
  return __spreadProps(__spreadValues(__spreadValues(__spreadValues({}, composite), hovercard), menu), {
    combobox,
    parent,
    menubar,
    hideAll: () => {
      hovercard.hide();
      parent == null ? void 0 : parent.hideAll();
    },
    setInitialFocus: (value) => menu.setState("initialFocus", value),
    setValues: (values) => menu.setState("values", values),
    setValue: (name, value) => {
      if (name === "__proto__")
        return;
      if (name === "constructor")
        return;
      if (Array.isArray(name))
        return;
      menu.setState("values", (values) => {
        const prevValue = values[name];
        const nextValue = applyState(value, prevValue);
        if (nextValue === prevValue)
          return values;
        return __spreadProps(__spreadValues({}, values), {
          [name]: nextValue !== void 0 && nextValue
        });
      });
    }
  });
}
export {
  createMenuStore
};
