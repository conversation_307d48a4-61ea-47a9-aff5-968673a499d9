{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,GAAG,CAAC;AAEnC,IAAM,UAAU,GAAuB;IACrC,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,MAAM,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,QAAQ,CAAC,kBAAkB,EAAE;4BACf,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { upperCaseFirst } from \".\";\n\nconst TEST_CASES: [string, string][] = [\n  [\"\", \"\"],\n  [\"test\", \"Test\"],\n  [\"TEST\", \"TEST\"],\n];\n\ndescribe(\"upper case first\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(upperCaseFirst(input)).toEqual(result);\n    });\n  }\n});\n"]}