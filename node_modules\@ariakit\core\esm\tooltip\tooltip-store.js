"use client";
import {
  createHovercardStore
} from "../__chunks/SOLWE6E5.js";
import "../__chunks/AF6IUUFN.js";
import "../__chunks/SX2XFD6A.js";
import "../__chunks/Z5IGYIPT.js";
import {
  createStore
} from "../__chunks/EAHJFCU4.js";
import {
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import {
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/tooltip/tooltip-store.ts
function createTooltipStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const hovercard = createHovercardStore(__spreadProps(__spreadValues({}, props), {
    placement: defaultValue(
      props.placement,
      syncState == null ? void 0 : syncState.placement,
      "top"
    ),
    hideTimeout: defaultValue(props.hideTimeout, syncState == null ? void 0 : syncState.hideTimeout, 0)
  }));
  const initialState = __spreadProps(__spreadValues({}, hovercard.getState()), {
    type: defaultValue(props.type, syncState == null ? void 0 : syncState.type, "description"),
    skipTimeout: defaultValue(props.skipTimeout, syncState == null ? void 0 : syncState.skipTimeout, 300)
  });
  const tooltip = createStore(initialState, hovercard, props.store);
  return __spreadValues(__spreadValues({}, hovercard), tooltip);
}
export {
  createTooltipStore
};
