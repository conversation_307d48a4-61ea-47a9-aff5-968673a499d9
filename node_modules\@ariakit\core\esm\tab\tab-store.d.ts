import type { CollectionStore, CollectionStoreItem } from "../collection/collection-store.js";
import type { CompositeStoreFunctions, CompositeStoreItem, CompositeStoreOptions, CompositeStoreState } from "../composite/composite-store.js";
import type { Store, StoreOptions, StoreProps } from "../utils/store.js";
import type { SetState } from "../utils/types.js";
export declare function createTabStore(props?: TabStoreProps): TabStore;
export interface TabStoreItem extends CompositeStoreItem {
    dimmed?: boolean;
}
export interface TabStorePanel extends CollectionStoreItem {
    tabId?: string | null;
}
export interface TabStoreState extends CompositeStoreState<TabStoreItem> {
    /** @default "horizontal" */
    orientation: CompositeStoreState<TabStoreItem>["orientation"];
    /** @default true */
    focusLoop: CompositeStoreState<TabStoreItem>["focusLoop"];
    /**
     * The id of the tab whose panel is currently visible. If it's `undefined`, it
     * will be automatically set to the first enabled tab.
     *
     * Live examples:
     * - [Tab with React Router](https://ariakit.org/examples/tab-react-router)
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     */
    selectedId: TabStoreState["activeId"];
    /**
     * Determines if the tab should be selected when it receives focus. If set to
     * `false`, the tab will only be selected upon clicking, not when
     * using arrow keys to shift focus.
     *
     * Live examples:
     * - [Tab with React Router](https://ariakit.org/examples/tab-react-router)
     * @default true
     */
    selectOnMove?: boolean;
}
export interface TabStoreFunctions extends CompositeStoreFunctions<TabStoreItem> {
    /**
     * Sets the
     * [`selectedId`](https://ariakit.org/reference/tab-provider#selectedid) state
     * without moving focus. If you want to move focus, use the
     * [`select`](https://ariakit.org/reference/use-tab-store#select) function
     * instead.
     * @example
     * // Selects the tab with id "tab-1"
     * store.setSelectedId("tab-1");
     * // Toggles between "tab-1" and "tab-2"
     * store.setSelectedId((id) => id === "tab-1" ? "tab-2" : "tab-1"));
     * // Selects the first tab
     * store.setSelectedId(store.first());
     * // Selects the next tab
     * store.setSelectedId(store.next());
     */
    setSelectedId: SetState<TabStoreState["selectedId"]>;
    /**
     * A collection store containing the tab panels.
     */
    panels: CollectionStore<TabStorePanel>;
    /**
     * Selects the tab for the given id and moves focus to it. If you want to set
     * the [`selectedId`](https://ariakit.org/reference/tab-provider#selectedid)
     * state without moving focus, use the
     * [`setSelectedId`](https://ariakit.org/reference/use-tab-store#setselectedid-1)
     * function instead.
     *
     * Live examples:
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     * @example
     * // Selects the tab with id "tab-1"
     * store.select("tab-1");
     * // Selects the first tab
     * store.select(store.first());
     * // Selects the next tab
     * store.select(store.next());
     */
    select: TabStore["move"];
}
export interface TabStoreOptions extends StoreOptions<TabStoreState, "orientation" | "focusLoop" | "selectedId" | "selectOnMove">, CompositeStoreOptions<TabStoreItem> {
    /**
     * The id of the tab whose panel is currently visible. If it's `undefined`, it
     * will be automatically set to the first enabled tab.
     *
     * Live examples:
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     */
    defaultSelectedId?: TabStoreState["selectedId"];
}
export interface TabStoreProps extends TabStoreOptions, StoreProps<TabStoreState> {
}
export interface TabStore extends TabStoreFunctions, Store<TabStoreState> {
}
