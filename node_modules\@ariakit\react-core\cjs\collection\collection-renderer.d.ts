import type { CSSProperties, ComponentPropsWithRef, ReactElement, ReactNode, RefCallback, RefObject } from "react";
import type { AnyObject, BooleanOrCallback, EmptyObject } from "@ariakit/core/utils/types";
import type { RenderProp } from "../utils/types.js";
import type { CollectionStore, CollectionStoreItem } from "./collection-store.js";
type NestedRendererItemProps = Pick<CollectionRendererOptions, "gap" | "orientation" | "itemSize" | "estimatedItemSize" | "padding" | "paddingStart" | "paddingEnd">;
interface ItemObject extends AnyObject, NestedRendererItemProps {
    /**
     * The item unique identifier. If not specified, an ID will be assigned based
     * on the item's index. If it's a nested item and the item is not included in
     * the `items` property of its parent, it must be composed by all the
     * ancestors' IDs and the item's ID, separated by slashes (e.g.,
     * `grand/parent/item`).
     */
    id?: string;
    /**
     * The item style object. If the `width` or `height` properties are
     * specificed, they will be used to calculate the item size.
     */
    style?: CSSProperties;
    /**
     * A list of nested items. Passing this property will help measure the item
     * size.
     */
    items?: Item[];
    /**
     * The rendered item element. This property is assigned to the `items` state
     * on the collection store and can be used to calculate the item size.
     */
    element?: HTMLElement | null;
}
type Item = ItemObject | Omit<string, string> | Omit<number, string> | Omit<boolean, string> | null | undefined;
type Items<T extends Item> = number | readonly T[];
interface BaseItemProps {
    id: string;
    ref: RefCallback<HTMLElement>;
    style: CSSProperties;
    index: number;
}
type ItemProps<T extends Item, P extends BaseItemProps = BaseItemProps> = unknown extends T ? P : P & (T extends AnyObject ? T : {
    value: T;
});
type RawItemProps<T extends Item> = unknown extends T ? EmptyObject : T extends AnyObject ? T : {
    value: T;
};
declare function getItemId(item: Item, index: number, baseId?: string): string;
declare function getItem<T extends Item = any>(items: Items<T>, index: number): RawItemProps<T> | null;
export declare function useCollectionRenderer<T extends Item = any>({ store, items: itemsProp, initialItems, gap, itemSize, estimatedItemSize, overscan: overscanProp, orientation: orientationProp, padding, paddingStart, paddingEnd, persistentIndices, renderOnScroll, renderOnResize, children: renderItem, ...props }: CollectionRendererProps<T>): {
    children: ReactNode[];
    ref?: ((instance: HTMLDivElement | null) => void) | RefObject<HTMLDivElement> | null | undefined;
    defaultChecked?: boolean | undefined;
    defaultValue?: string | number | readonly string[] | undefined;
    suppressContentEditableWarning?: boolean | undefined;
    suppressHydrationWarning?: boolean | undefined;
    accessKey?: string | undefined;
    autoFocus?: boolean | undefined;
    className?: string | undefined;
    contentEditable?: "inherit" | (boolean | "false" | "true") | "plaintext-only" | undefined;
    contextMenu?: string | undefined;
    dir?: string | undefined;
    draggable?: (boolean | "false" | "true") | undefined;
    hidden?: boolean | undefined;
    id?: string | undefined;
    lang?: string | undefined;
    nonce?: string | undefined;
    slot?: string | undefined;
    spellCheck?: (boolean | "false" | "true") | undefined;
    style?: CSSProperties | undefined;
    tabIndex?: number | undefined;
    title?: string | undefined;
    translate?: "no" | "yes" | undefined;
    radioGroup?: string | undefined;
    role?: import("react").AriaRole | undefined;
    about?: string | undefined;
    content?: string | undefined;
    datatype?: string | undefined;
    inlist?: any;
    prefix?: string | undefined;
    property?: string | undefined;
    rel?: string | undefined;
    resource?: string | undefined;
    rev?: string | undefined;
    typeof?: string | undefined;
    vocab?: string | undefined;
    autoCapitalize?: string | undefined;
    autoCorrect?: string | undefined;
    autoSave?: string | undefined;
    color?: string | undefined;
    itemProp?: string | undefined;
    itemScope?: boolean | undefined;
    itemType?: string | undefined;
    itemID?: string | undefined;
    itemRef?: string | undefined;
    results?: number | undefined;
    security?: string | undefined;
    unselectable?: "on" | "off" | undefined;
    inputMode?: "search" | "none" | "text" | "email" | "tel" | "url" | "numeric" | "decimal" | undefined;
    is?: string | undefined;
    "aria-activedescendant"?: string | undefined;
    "aria-atomic"?: (boolean | "false" | "true") | undefined;
    "aria-autocomplete"?: "both" | "none" | "inline" | "list" | undefined;
    "aria-braillelabel"?: string | undefined;
    "aria-brailleroledescription"?: string | undefined;
    "aria-busy"?: (boolean | "false" | "true") | undefined;
    "aria-checked"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-colcount"?: number | undefined;
    "aria-colindex"?: number | undefined;
    "aria-colindextext"?: string | undefined;
    "aria-colspan"?: number | undefined;
    "aria-controls"?: string | undefined;
    "aria-current"?: boolean | "page" | "false" | "true" | "time" | "date" | "step" | "location" | undefined;
    "aria-describedby"?: string | undefined;
    "aria-description"?: string | undefined;
    "aria-details"?: string | undefined;
    "aria-disabled"?: (boolean | "false" | "true") | undefined;
    "aria-dropeffect"?: "link" | "none" | "copy" | "move" | "execute" | "popup" | undefined;
    "aria-errormessage"?: string | undefined;
    "aria-expanded"?: (boolean | "false" | "true") | undefined;
    "aria-flowto"?: string | undefined;
    "aria-grabbed"?: (boolean | "false" | "true") | undefined;
    "aria-haspopup"?: boolean | "listbox" | "grid" | "menu" | "false" | "true" | "dialog" | "tree" | undefined;
    "aria-hidden"?: (boolean | "false" | "true") | undefined;
    "aria-invalid"?: boolean | "false" | "true" | "grammar" | "spelling" | undefined;
    "aria-keyshortcuts"?: string | undefined;
    "aria-label"?: string | undefined;
    "aria-labelledby"?: string | undefined;
    "aria-level"?: number | undefined;
    "aria-live"?: "off" | "assertive" | "polite" | undefined;
    "aria-modal"?: (boolean | "false" | "true") | undefined;
    "aria-multiline"?: (boolean | "false" | "true") | undefined;
    "aria-multiselectable"?: (boolean | "false" | "true") | undefined;
    "aria-orientation"?: "horizontal" | "vertical" | undefined;
    "aria-owns"?: string | undefined;
    "aria-placeholder"?: string | undefined;
    "aria-posinset"?: number | undefined;
    "aria-pressed"?: boolean | "mixed" | "false" | "true" | undefined;
    "aria-readonly"?: (boolean | "false" | "true") | undefined;
    "aria-relevant"?: "all" | "text" | "additions" | "additions removals" | "additions text" | "removals" | "removals additions" | "removals text" | "text additions" | "text removals" | undefined;
    "aria-required"?: (boolean | "false" | "true") | undefined;
    "aria-roledescription"?: string | undefined;
    "aria-rowcount"?: number | undefined;
    "aria-rowindex"?: number | undefined;
    "aria-rowindextext"?: string | undefined;
    "aria-rowspan"?: number | undefined;
    "aria-selected"?: (boolean | "false" | "true") | undefined;
    "aria-setsize"?: number | undefined;
    "aria-sort"?: "none" | "ascending" | "descending" | "other" | undefined;
    "aria-valuemax"?: number | undefined;
    "aria-valuemin"?: number | undefined;
    "aria-valuenow"?: number | undefined;
    "aria-valuetext"?: string | undefined;
    dangerouslySetInnerHTML?: {
        __html: string | TrustedHTML;
    } | undefined;
    onCopy?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
    onCopyCapture?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
    onCut?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
    onCutCapture?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
    onPaste?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
    onPasteCapture?: import("react").ClipboardEventHandler<HTMLDivElement> | undefined;
    onCompositionEnd?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
    onCompositionEndCapture?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
    onCompositionStart?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
    onCompositionStartCapture?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
    onCompositionUpdate?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
    onCompositionUpdateCapture?: import("react").CompositionEventHandler<HTMLDivElement> | undefined;
    onFocus?: import("react").FocusEventHandler<HTMLDivElement> | undefined;
    onFocusCapture?: import("react").FocusEventHandler<HTMLDivElement> | undefined;
    onBlur?: import("react").FocusEventHandler<HTMLDivElement> | undefined;
    onBlurCapture?: import("react").FocusEventHandler<HTMLDivElement> | undefined;
    onChange?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onChangeCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onBeforeInput?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onBeforeInputCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onInput?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onInputCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onReset?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onResetCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onSubmit?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onSubmitCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onInvalid?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onInvalidCapture?: import("react").FormEventHandler<HTMLDivElement> | undefined;
    onLoad?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onError?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onErrorCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onKeyDown?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
    onKeyDownCapture?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
    onKeyPress?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
    onKeyPressCapture?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
    onKeyUp?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
    onKeyUpCapture?: import("react").KeyboardEventHandler<HTMLDivElement> | undefined;
    onAbort?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onAbortCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onCanPlay?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onCanPlayCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onCanPlayThrough?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onCanPlayThroughCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onDurationChange?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onDurationChangeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onEmptied?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onEmptiedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onEncrypted?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onEncryptedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onEnded?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onEndedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadedData?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadedDataCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadedMetadata?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadedMetadataCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadStart?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onLoadStartCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onPause?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onPauseCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onPlay?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onPlayCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onPlaying?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onPlayingCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onProgress?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onProgressCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onRateChange?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onRateChangeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onResize?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onResizeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSeeked?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSeekedCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSeeking?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSeekingCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onStalled?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onStalledCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSuspend?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSuspendCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onTimeUpdate?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onTimeUpdateCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onVolumeChange?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onVolumeChangeCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onWaiting?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onWaitingCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onAuxClick?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onAuxClickCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onClick?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onClickCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onContextMenu?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onContextMenuCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onDoubleClick?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onDoubleClickCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onDrag?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragEnd?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragEndCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragEnter?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragEnterCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragExit?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragExitCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragLeave?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragLeaveCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragOver?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragOverCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragStart?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDragStartCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDrop?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onDropCapture?: import("react").DragEventHandler<HTMLDivElement> | undefined;
    onMouseDown?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseDownCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseEnter?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseLeave?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseMove?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseMoveCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseOut?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseOutCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseOver?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseOverCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseUp?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onMouseUpCapture?: import("react").MouseEventHandler<HTMLDivElement> | undefined;
    onSelect?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onSelectCapture?: import("react").ReactEventHandler<HTMLDivElement> | undefined;
    onTouchCancel?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchCancelCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchEnd?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchEndCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchMove?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchMoveCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchStart?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onTouchStartCapture?: import("react").TouchEventHandler<HTMLDivElement> | undefined;
    onPointerDown?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerDownCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerMove?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerMoveCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerUp?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerUpCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerCancel?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerCancelCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerEnter?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerEnterCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerLeave?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerLeaveCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerOver?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerOverCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerOut?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onPointerOutCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onGotPointerCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onGotPointerCaptureCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onLostPointerCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onLostPointerCaptureCapture?: import("react").PointerEventHandler<HTMLDivElement> | undefined;
    onScroll?: import("react").UIEventHandler<HTMLDivElement> | undefined;
    onScrollCapture?: import("react").UIEventHandler<HTMLDivElement> | undefined;
    onWheel?: import("react").WheelEventHandler<HTMLDivElement> | undefined;
    onWheelCapture?: import("react").WheelEventHandler<HTMLDivElement> | undefined;
    onAnimationStart?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
    onAnimationStartCapture?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
    onAnimationEnd?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
    onAnimationEndCapture?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
    onAnimationIteration?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
    onAnimationIterationCapture?: import("react").AnimationEventHandler<HTMLDivElement> | undefined;
    onTransitionEnd?: import("react").TransitionEventHandler<HTMLDivElement> | undefined;
    onTransitionEndCapture?: import("react").TransitionEventHandler<HTMLDivElement> | undefined;
    key?: import("react").Key | null | undefined;
    render?: ReactElement<any, string | import("react").JSXElementConstructor<any>> | RenderProp | undefined;
};
export declare const CollectionRenderer: <T extends Item = any>(props: CollectionRendererProps<T>) => ReactElement<any, string | import("react").JSXElementConstructor<any>>;
export declare const getCollectionRendererItem: typeof getItem;
export declare const getCollectionRendererItemId: typeof getItemId;
export type CollectionRendererItemObject = ItemObject;
export type CollectionRendererItem = Item;
export type CollectionRendererBaseItemProps = BaseItemProps;
export type CollectionRendererItemProps<T extends Item, P extends BaseItemProps = BaseItemProps> = ItemProps<T, P>;
export interface CollectionRendererOptions<T extends Item = any> {
    /**
     * Object returned by the
     * [`useCollectionStore`](https://ariakit.org/reference/use-collection-store)
     * hook. If not provided, the closest
     * [Collection](https://ariakit.org/components/collection) component's
     * context will be used.
     *
     * The store
     * [`items`](https://ariakit.org/reference/use-collection-store#items) state
     * will be used to render the items if the
     * [`items`](https://ariakit.org/reference/collection-items#items) prop is not
     * provided.
     */
    store?: CollectionStore<T extends CollectionStoreItem ? T : CollectionStoreItem>;
    /**
     * All items to be rendered. This prop can be either a memoized array of items
     * or a number representing the total number of items to be rendered.
     *
     * When passing an array, each item can be either a primitive value or an
     * object. If it's a primitive value, an object with the `value` property will
     * be automatically created for each item and passed as an argument to the
     * function that renders the item. If it's an object, the entire object will
     * be passed.
     *
     * The item object can have any shape, but some **optional** properties have
     * particular functions:
     * - `id`: The same as the HTML attribute. If not provided, one will be
     *   generated automatically.
     * - `style`: The same as the HTML attribute. This will be merged with the
     *   styles generated by the component for each item. If the `width` or
     *   `height` properties are explicitly provided here, they will be used to
     *   calculate the item's size. This is useful when rendering items with known
     *   variable sizes.
     * - `items`: An array of items to be rendered as children of this item. This
     *   is useful when rendering nested items. This property is recommended when
     *   rendering nested items because it will help the parent renderer calculate
     *   the size and position of the nested items.
     *
     * Also, When rendering nested renderers, you can optionally include props
     * like `gap`, `orientation`, `itemSize`, `estimatedItemSize`, `padding`,
     * `paddingStart`, and `paddingEnd`. These props will help the parent renderer
     * calculate the size and position of the nested renderers.
     */
    items?: Items<T>;
    /**
     * Whether the items should be rendered when the closest scrollable ancestor
     * is scrolled.
     * @default true
     */
    renderOnScroll?: BooleanOrCallback<Event>;
    /**
     * Whether the items should be rendered when the closest scrollable ancestor
     * is resized.
     * @default true
     */
    renderOnResize?: BooleanOrCallback<Element>;
    /**
     * The number of items to render initially. Can be set to a negative number to
     * render items from the end of the list.
     * @default 0
     */
    initialItems?: number;
    /**
     * Whether the items should be rendered vertically or horizontally.
     * @default "vertical"
     */
    orientation?: "vertical" | "horizontal";
    /**
     * The fixed size of each item in pixels. If not provided, the size will be
     * automatically calculated.
     */
    itemSize?: number;
    /**
     * The estimated size of each item in pixels. This is used to calculate the
     * initial size of the items before they are rendered.
     * @default 40
     */
    estimatedItemSize?: number;
    /**
     * The gap between each item in pixels.
     * @default 0
     */
    gap?: number;
    /**
     * The number of items to render before and after the visible items.
     * @default 1
     */
    overscan?: number;
    /**
     * The item indices that should always be rendered.
     */
    persistentIndices?: number[];
    /**
     * The padding between the items and the container in pixels. This value will
     * be used for both the `paddingStart` and `paddingEnd` props, if they are not
     * explicitly provided.
     * @default 0
     */
    padding?: number;
    /**
     * The padding between the items and the container's start edge in pixels.
     * This value will override the `padding` prop if it is explicitly provided.
     * @default 0
     */
    paddingStart?: number;
    /**
     * The padding between the items and the container's end edge in pixels. This
     * value will override the `padding` prop if it is explicitly provided.
     * @default 0
     */
    paddingEnd?: number;
    /**
     * The `children` should be a function that receives item props and returns a
     * React element. The item props should be spread onto the element that
     * renders the item.
     */
    children?: (item: ItemProps<T>) => ReactNode;
    render?: RenderProp | ReactElement;
}
export interface CollectionRendererProps<T extends Item = any> extends Omit<ComponentPropsWithRef<"div">, "children">, CollectionRendererOptions<T> {
}
export {};
