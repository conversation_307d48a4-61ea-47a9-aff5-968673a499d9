"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";







var _5F4DVUNScjs = require('../__chunks/5F4DVUNS.cjs');


var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/utils/focus.ts
var selector = "input:not([type='hidden']):not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], iframe, object, embed, area[href], audio[controls], video[controls], [contenteditable]:not([contenteditable='false'])";
function hasNegativeTabIndex(element) {
  const tabIndex = parseInt(element.getAttribute("tabindex") || "0", 10);
  return tabIndex < 0;
}
function isFocusable(element) {
  if (!_5F4DVUNScjs.matches.call(void 0, element, selector))
    return false;
  if (!_5F4DVUNScjs.isVisible.call(void 0, element))
    return false;
  if (_5F4DVUNScjs.closest.call(void 0, element, "[inert]"))
    return false;
  return true;
}
function isTabbable(element) {
  if (!isFocusable(element))
    return false;
  if (hasNegativeTabIndex(element))
    return false;
  if (!("form" in element))
    return true;
  if (!element.form)
    return true;
  if (element.checked)
    return true;
  if (element.type !== "radio")
    return true;
  const radioGroup = element.form.elements.namedItem(element.name);
  if (!radioGroup)
    return true;
  if (!("length" in radioGroup))
    return true;
  const activeElement = _5F4DVUNScjs.getActiveElement.call(void 0, element);
  if (!activeElement)
    return true;
  if (activeElement === element)
    return true;
  if (!("form" in activeElement))
    return true;
  if (activeElement.form !== element.form)
    return true;
  if (activeElement.name !== element.name)
    return true;
  return false;
}
function getAllFocusableIn(container, includeContainer) {
  const elements = Array.from(
    container.querySelectorAll(selector)
  );
  if (includeContainer) {
    elements.unshift(container);
  }
  const focusableElements = elements.filter(isFocusable);
  focusableElements.forEach((element, i) => {
    if (_5F4DVUNScjs.isFrame.call(void 0, element) && element.contentDocument) {
      const frameBody = element.contentDocument.body;
      focusableElements.splice(i, 1, ...getAllFocusableIn(frameBody));
    }
  });
  return focusableElements;
}
function getAllFocusable(includeBody) {
  return getAllFocusableIn(document.body, includeBody);
}
function getFirstFocusableIn(container, includeContainer) {
  const [first] = getAllFocusableIn(container, includeContainer);
  return first || null;
}
function getFirstFocusable(includeBody) {
  return getFirstFocusableIn(document.body, includeBody);
}
function getAllTabbableIn(container, includeContainer, fallbackToFocusable) {
  const elements = Array.from(
    container.querySelectorAll(selector)
  );
  const tabbableElements = elements.filter(isTabbable);
  if (includeContainer && isTabbable(container)) {
    tabbableElements.unshift(container);
  }
  tabbableElements.forEach((element, i) => {
    if (_5F4DVUNScjs.isFrame.call(void 0, element) && element.contentDocument) {
      const frameBody = element.contentDocument.body;
      const allFrameTabbable = getAllTabbableIn(
        frameBody,
        false,
        fallbackToFocusable
      );
      tabbableElements.splice(i, 1, ...allFrameTabbable);
    }
  });
  if (!tabbableElements.length && fallbackToFocusable) {
    return elements;
  }
  return tabbableElements;
}
function getAllTabbable(fallbackToFocusable) {
  return getAllTabbableIn(document.body, false, fallbackToFocusable);
}
function getFirstTabbableIn(container, includeContainer, fallbackToFocusable) {
  const [first] = getAllTabbableIn(
    container,
    includeContainer,
    fallbackToFocusable
  );
  return first || null;
}
function getFirstTabbable(fallbackToFocusable) {
  return getFirstTabbableIn(document.body, false, fallbackToFocusable);
}
function getLastTabbableIn(container, includeContainer, fallbackToFocusable) {
  const allTabbable = getAllTabbableIn(
    container,
    includeContainer,
    fallbackToFocusable
  );
  return allTabbable[allTabbable.length - 1] || null;
}
function getLastTabbable(fallbackToFocusable) {
  return getLastTabbableIn(document.body, false, fallbackToFocusable);
}
function getNextTabbableIn(container, includeContainer, fallbackToFirst, fallbackToFocusable) {
  const activeElement = _5F4DVUNScjs.getActiveElement.call(void 0, container);
  const allFocusable = getAllFocusableIn(container, includeContainer);
  const activeIndex = allFocusable.indexOf(activeElement);
  const nextFocusableElements = allFocusable.slice(activeIndex + 1);
  return nextFocusableElements.find(isTabbable) || (fallbackToFirst ? allFocusable.find(isTabbable) : null) || (fallbackToFocusable ? nextFocusableElements[0] : null) || null;
}
function getNextTabbable(fallbackToFirst, fallbackToFocusable) {
  return getNextTabbableIn(
    document.body,
    false,
    fallbackToFirst,
    fallbackToFocusable
  );
}
function getPreviousTabbableIn(container, includeContainer, fallbackToLast, fallbackToFocusable) {
  const activeElement = _5F4DVUNScjs.getActiveElement.call(void 0, container);
  const allFocusable = getAllFocusableIn(container, includeContainer).reverse();
  const activeIndex = allFocusable.indexOf(activeElement);
  const previousFocusableElements = allFocusable.slice(activeIndex + 1);
  return previousFocusableElements.find(isTabbable) || (fallbackToLast ? allFocusable.find(isTabbable) : null) || (fallbackToFocusable ? previousFocusableElements[0] : null) || null;
}
function getPreviousTabbable(fallbackToFirst, fallbackToFocusable) {
  return getPreviousTabbableIn(
    document.body,
    false,
    fallbackToFirst,
    fallbackToFocusable
  );
}
function getClosestFocusable(element) {
  while (element && !isFocusable(element)) {
    element = _5F4DVUNScjs.closest.call(void 0, element, selector);
  }
  return element || null;
}
function hasFocus(element) {
  const activeElement = _5F4DVUNScjs.getActiveElement.call(void 0, element);
  if (!activeElement)
    return false;
  if (activeElement === element)
    return true;
  const activeDescendant = activeElement.getAttribute("aria-activedescendant");
  if (!activeDescendant)
    return false;
  return activeDescendant === element.id;
}
function hasFocusWithin(element) {
  const activeElement = _5F4DVUNScjs.getActiveElement.call(void 0, element);
  if (!activeElement)
    return false;
  if (_5F4DVUNScjs.contains.call(void 0, element, activeElement))
    return true;
  const activeDescendant = activeElement.getAttribute("aria-activedescendant");
  if (!activeDescendant)
    return false;
  if (!("id" in element))
    return false;
  if (activeDescendant === element.id)
    return true;
  return !!element.querySelector(`#${CSS.escape(activeDescendant)}`);
}
function focusIfNeeded(element) {
  if (!hasFocusWithin(element) && isFocusable(element)) {
    element.focus();
  }
}
function disableFocus(element) {
  var _a;
  const currentTabindex = (_a = element.getAttribute("tabindex")) != null ? _a : "";
  element.setAttribute("data-tabindex", currentTabindex);
  element.setAttribute("tabindex", "-1");
}
function disableFocusIn(container, includeContainer) {
  const tabbableElements = getAllTabbableIn(container, includeContainer);
  tabbableElements.forEach(disableFocus);
}
function restoreFocusIn(container) {
  const elements = container.querySelectorAll("[data-tabindex]");
  const restoreTabIndex = (element) => {
    const tabindex = element.getAttribute("data-tabindex");
    element.removeAttribute("data-tabindex");
    if (tabindex) {
      element.setAttribute("tabindex", tabindex);
    } else {
      element.removeAttribute("tabindex");
    }
  };
  if (container.hasAttribute("data-tabindex")) {
    restoreTabIndex(container);
  }
  elements.forEach(restoreTabIndex);
}
function focusIntoView(element, options) {
  if (!("scrollIntoView" in element)) {
    element.focus();
  } else {
    element.focus({ preventScroll: true });
    element.scrollIntoView(_AV6KTKLEcjs.__spreadValues.call(void 0, { block: "nearest", inline: "nearest" }, options));
  }
}

























exports.disableFocus = disableFocus; exports.disableFocusIn = disableFocusIn; exports.focusIfNeeded = focusIfNeeded; exports.focusIntoView = focusIntoView; exports.getAllFocusable = getAllFocusable; exports.getAllFocusableIn = getAllFocusableIn; exports.getAllTabbable = getAllTabbable; exports.getAllTabbableIn = getAllTabbableIn; exports.getClosestFocusable = getClosestFocusable; exports.getFirstFocusable = getFirstFocusable; exports.getFirstFocusableIn = getFirstFocusableIn; exports.getFirstTabbable = getFirstTabbable; exports.getFirstTabbableIn = getFirstTabbableIn; exports.getLastTabbable = getLastTabbable; exports.getLastTabbableIn = getLastTabbableIn; exports.getNextTabbable = getNextTabbable; exports.getNextTabbableIn = getNextTabbableIn; exports.getPreviousTabbable = getPreviousTabbable; exports.getPreviousTabbableIn = getPreviousTabbableIn; exports.hasFocus = hasFocus; exports.hasFocusWithin = hasFocusWithin; exports.isFocusable = isFocusable; exports.isTabbable = isTabbable; exports.restoreFocusIn = restoreFocusIn;
