"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { newObj[key] = obj[key]; } } } newObj.default = obj; return newObj; } }"use client";


var _NRGWVSB5cjs = require('../__chunks/NRGWVSB5.cjs');
require('../__chunks/W5LJEMLB.cjs');
require('../__chunks/JAQJG42R.cjs');


var _OLOZ5JT2cjs = require('../__chunks/OLOZ5JT2.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');
require('../__chunks/AV6KTKLE.cjs');

// src/composite/composite-overflow-store.ts
var _compositeoverflowstore = require('@ariakit/core/composite/composite-overflow-store'); var Core = _interopRequireWildcard(_compositeoverflowstore);
function useCompositeOverflowStoreProps(store, update, props) {
  return _NRGWVSB5cjs.usePopoverStoreProps.call(void 0, store, update, props);
}
function useCompositeOverflowStore(props = {}) {
  const [store, update] = _OLOZ5JT2cjs.useStore.call(void 0, Core.createCompositeOverflowStore, props);
  return useCompositeOverflowStoreProps(store, update, props);
}



exports.useCompositeOverflowStore = useCompositeOverflowStore; exports.useCompositeOverflowStoreProps = useCompositeOverflowStoreProps;
