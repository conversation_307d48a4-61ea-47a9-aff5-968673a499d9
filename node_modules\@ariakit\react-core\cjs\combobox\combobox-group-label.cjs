"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _TUDQDX77cjs = require('../__chunks/TUDQDX77.cjs');
require('../__chunks/QQCFY3MB.cjs');
require('../__chunks/FE3YCURH.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');
require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-group-label.ts
var useComboboxGroupLabel = _RNZNGEL4cjs.createHook.call(void 0, 
  (props) => {
    props = _TUDQDX77cjs.useCompositeGroupLabel.call(void 0, props);
    return props;
  }
);
var ComboboxGroupLabel = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxGroupLabel(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "div", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxGroupLabel.displayName = "ComboboxGroupLabel";
}



exports.ComboboxGroupLabel = ComboboxGroupLabel; exports.useComboboxGroupLabel = useComboboxGroupLabel;
