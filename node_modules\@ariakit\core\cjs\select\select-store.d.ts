import type { ComboboxStore } from "../combobox/combobox-store.js";
import type { CompositeStoreFunctions, CompositeStoreItem, CompositeStoreOptions, CompositeStoreState } from "../composite/composite-store.js";
import type { PopoverStoreFunctions, PopoverStoreOptions, PopoverStoreState } from "../popover/popover-store.js";
import type { Store, StoreOptions, StoreProps } from "../utils/store.js";
import type { PickRequired, SetState } from "../utils/types.js";
type MutableValue<T extends SelectStoreValue = SelectStoreValue> = T extends string ? string : T;
export declare function createSelectStore<T extends SelectStoreValue = SelectStoreValue>(props: PickRequired<SelectStoreProps<T>, "value" | "defaultValue">): SelectStore<T>;
export declare function createSelectStore(props?: SelectStoreProps): SelectStore;
export type SelectStoreValue = string | string[];
export interface SelectStoreItem extends CompositeStoreItem {
    value?: string;
}
export interface SelectStoreState<T extends SelectStoreValue = SelectStoreValue> extends CompositeStoreState<SelectStoreItem>, PopoverStoreState {
    /** @default true */
    virtualFocus: CompositeStoreState<SelectStoreItem>["virtualFocus"];
    /** @default false */
    includesBaseElement: CompositeStoreState<SelectStoreItem>["includesBaseElement"];
    /** @default null */
    activeId: CompositeStoreState<SelectStoreItem>["activeId"];
    /** @default "vertical" */
    orientation: CompositeStoreState<SelectStoreItem>["orientation"];
    /** @default "bottom-start" */
    placement: PopoverStoreState["placement"];
    /**
     * The select value.
     *
     * Live examples:
     * - [Form with Select](https://ariakit.org/examples/form-select)
     * - [Select Grid](https://ariakit.org/examples/select-grid)
     * - [Select with custom
     *   items](https://ariakit.org/examples/select-item-custom)
     * - [Multi-Select](https://ariakit.org/examples/select-multiple)
     * - [Toolbar with Select](https://ariakit.org/examples/toolbar-select)
     */
    value: MutableValue<T>;
    /**
     * Whether the select
     * [`value`](https://ariakit.org/reference/select-provider#value) should be
     * set when the active item changes by moving (which usually happens when
     * moving to an item using the keyboard).
     *
     * Live examples:
     * - [Select Grid](https://ariakit.org/examples/select-grid)
     * - [Select with custom
     *   items](https://ariakit.org/examples/select-item-custom)
     * @default false
     */
    setValueOnMove: boolean;
    /**
     * The select button element.
     *
     * Live examples:
     * - [Form with Select](https://ariakit.org/examples/form-select)
     */
    selectElement: HTMLElement | null;
    /**
     * The select label element.
     */
    labelElement: HTMLElement | null;
}
export interface SelectStoreFunctions<T extends SelectStoreValue = SelectStoreValue> extends Pick<SelectStoreOptions<T>, "combobox">, CompositeStoreFunctions<SelectStoreItem>, PopoverStoreFunctions {
    /**
     * Sets the [`value`](https://ariakit.org/reference/select-provider#value)
     * state.
     * @example
     * store.setValue("Apple");
     * store.setValue(["Apple", "Banana"]);
     * store.setValue((value) => value === "Apple" ? "Banana" : "Apple"));
     */
    setValue: SetState<SelectStoreState<T>["value"]>;
    /**
     * Sets the `selectElement` state.
     */
    setSelectElement: SetState<SelectStoreState<T>["selectElement"]>;
    /**
     * Sets the `labelElement` state.
     */
    setLabelElement: SetState<SelectStoreState<T>["labelElement"]>;
}
export interface SelectStoreOptions<T extends SelectStoreValue = SelectStoreValue> extends CompositeStoreOptions<SelectStoreItem>, PopoverStoreOptions, StoreOptions<SelectStoreState<T>, "virtualFocus" | "includesBaseElement" | "activeId" | "orientation" | "placement" | "value" | "setValueOnMove"> {
    /**
     * A reference to a combobox store. This is used when combining the combobox
     * with a select (e.g., select with a search input). The stores will share the
     * same state.
     */
    combobox?: ComboboxStore | null;
    /**
     * The default value. If not set, the first non-disabled item will be used.
     *
     * Live examples:
     * - [Form with Select](https://ariakit.org/examples/form-select)
     * - [Animated Select](https://ariakit.org/examples/select-animated)
     * - [Select with Combobox](https://ariakit.org/examples/select-combobox)
     * - [SelectGroup](https://ariakit.org/examples/select-group)
     */
    defaultValue?: SelectStoreState<T>["value"];
}
export interface SelectStoreProps<T extends SelectStoreValue = SelectStoreValue> extends SelectStoreOptions<T>, StoreProps<SelectStoreState<T>> {
}
export interface SelectStore<T extends SelectStoreValue = SelectStoreValue> extends SelectStoreFunctions<T>, Store<SelectStoreState<T>> {
}
export {};
