"use client";
import {
  createCompositeStore
} from "../__chunks/IERTEJ3A.js";
import "../__chunks/22K762VQ.js";
import {
  createStore
} from "../__chunks/EAHJFCU4.js";
import {
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import "../__chunks/DLOEKDPY.js";
import "../__chunks/7PRQYBBV.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/radio/radio-store.ts
function createRadioStore(_a = {}) {
  var props = __objRest(_a, []);
  var _a2;
  const syncState = (_a2 = props.store) == null ? void 0 : _a2.getState();
  const composite = createCompositeStore(__spreadProps(__spreadValues({}, props), {
    focusLoop: defaultValue(props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
  const initialState = __spreadProps(__spreadValues({}, composite.getState()), {
    value: defaultValue(
      props.value,
      syncState == null ? void 0 : syncState.value,
      props.defaultValue,
      null
    )
  });
  const radio = createStore(initialState, composite, props.store);
  return __spreadProps(__spreadValues(__spreadValues({}, composite), radio), {
    setValue: (value) => radio.setState("value", value)
  });
}
export {
  createRadioStore
};
