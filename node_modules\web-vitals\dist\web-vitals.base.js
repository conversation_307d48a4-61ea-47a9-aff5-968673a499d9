var e,n=-1,t=function(e){addEventListener("pageshow",(function(t){t.persisted&&(n=t.timeStamp,e(t))}),!0)},i=function(){return window.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,n=performance.navigation.type,t={entryType:"navigation",startTime:0,type:2==n?"back_forward":1===n?"reload":"navigate"};for(var i in e)"navigationStart"!==i&&"toJSON"!==i&&(t[i]=Math.max(e[i]-e.navigationStart,0));return t}())},r=function(){var e=i();return e&&e.activationStart||0},a=function(e,t){var a=i(),o="navigate";n>=0?o="back-forward-cache":a&&(document.prerendering||r()>0?o="prerender":document.wasDiscarded?o="restore":a.type&&(o=a.type.replace(/_/g,"-")));return{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:o}},o=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},c=function(e,n,t,i){var r,a;return function(o){n.value>=0&&(o||i)&&((a=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=a,n.rating=function(e,n){return e>n[1]?"poor":e>n[0]?"needs-improvement":"good"}(n.value,t),e(n))}},u=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},s=function(e){var n=function(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||e(n)};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},f=function(e){var n=!1;return function(t){n||(e(t),n=!0)}},d=-1,l=function(e){"hidden"===document.visibilityState&&d>-1&&(d="visibilitychange"===e.type?e.timeStamp:0,p())},v=function(){addEventListener("visibilitychange",l,!0),addEventListener("prerenderingchange",l,!0)},p=function(){removeEventListener("visibilitychange",l,!0),removeEventListener("prerenderingchange",l,!0)},m=function(){return d<0&&((d=window.webVitals.firstHiddenTime)===1/0&&v(),t((function(){setTimeout((function(){d="hidden"!==document.visibilityState||document.prerendering?1/0:0,v()}),0)}))),{get firstHiddenTime(){return d}}},h=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},g=[1800,3e3],y=function(e,n){n=n||{},h((function(){var i,s=m(),f=a("FCP"),d=o("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(d.disconnect(),e.startTime<s.firstHiddenTime&&(f.value=Math.max(e.startTime-r(),0),f.entries.push(e),i(!0)))}))}));d&&(i=c(e,f,g,n.reportAllChanges),t((function(t){f=a("FCP"),i=c(e,f,g,n.reportAllChanges),u((function(){f.value=performance.now()-t.timeStamp,i(!0)}))})))}))},T=[.1,.25],w=function(e,n){n=n||{},y(f((function(){var i,r=a("CLS",0),f=0,d=[],l=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=d[0],t=d[d.length-1];f&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(f+=e.value,d.push(e)):(f=e.value,d=[e])}})),f>r.value&&(r.value=f,r.entries=d,i())},v=o("layout-shift",l);v&&(i=c(e,r,T,n.reportAllChanges),s((function(){l(v.takeRecords()),i(!0)})),t((function(){f=0,r=a("CLS",0),i=c(e,r,T,n.reportAllChanges),u((function(){return i()}))})),setTimeout(i,0))})))},b=[100,300],C=function(e,n){n=n||{},h((function(){var i,r=m(),u=a("FID"),d=function(e){e.startTime<r.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),i(!0))},l=function(e){e.forEach(d)},v=o("first-input",l);i=c(e,u,b,n.reportAllChanges),v&&s(f((function(){l(v.takeRecords()),v.disconnect()}))),console.warn('The web-vitals "base+polyfill" build is deprecated. See: https://bit.ly/3aqzsGm'),v||window.webVitals.firstInputPolyfill(d),t((function(){u=a("FID"),i=c(e,u,b,n.reportAllChanges),window.webVitals.resetFirstInputPolyfill(),window.webVitals.firstInputPolyfill(d)}))}))},E=0,S=1/0,I=0,P=function(e){e.forEach((function(e){e.interactionId&&(S=Math.min(S,e.interactionId),I=Math.max(I,e.interactionId),E=I?(I-S)/7+1:0)}))},L=function(){return e?E:performance.interactionCount||0},A=function(){"interactionCount"in performance||e||(e=o("event",P,{type:"event",buffered:!0,durationThreshold:0}))},F=[200,500],M=0,k=function(){return L()-M},x=[],B={},D=function(e){var n=x[x.length-1],t=B[e.interactionId];if(t||x.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};B[i.id]=i,x.push(i)}x.sort((function(e,n){return n.latency-e.latency})),x.splice(10).forEach((function(e){delete B[e.id]}))}},H=function(e,n){n=n||{},h((function(){var i;A();var r,u=a("INP"),f=function(e){e.forEach((function(e){(e.interactionId&&D(e),"first-input"===e.entryType)&&(!x.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&D(e))}));var n,t=(n=Math.min(x.length-1,Math.floor(k()/50)),x[n]);t&&t.latency!==u.value&&(u.value=t.latency,u.entries=t.entries,r())},d=o("event",f,{durationThreshold:null!==(i=n.durationThreshold)&&void 0!==i?i:40});r=c(e,u,F,n.reportAllChanges),d&&("PerformanceEventTiming"in window&&"interactionId"in PerformanceEventTiming.prototype&&d.observe({type:"first-input",buffered:!0}),s((function(){f(d.takeRecords()),u.value<0&&k()>0&&(u.value=0,u.entries=[]),r(!0)})),t((function(){x=[],M=L(),u=a("INP"),r=c(e,u,F,n.reportAllChanges)})))}))},R=[2500,4e3],N={},O=function(e,n){n=n||{},h((function(){var i,d=m(),l=a("LCP"),v=function(e){var n=e[e.length-1];n&&n.startTime<d.firstHiddenTime&&(l.value=Math.max(n.startTime-r(),0),l.entries=[n],i())},p=o("largest-contentful-paint",v);if(p){i=c(e,l,R,n.reportAllChanges);var h=f((function(){N[l.id]||(v(p.takeRecords()),p.disconnect(),N[l.id]=!0,i(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return setTimeout(h,0)}),!0)})),s(h),t((function(t){l=a("LCP"),i=c(e,l,R,n.reportAllChanges),u((function(){l.value=performance.now()-t.timeStamp,N[l.id]=!0,i(!0)}))}))}}))},V=[800,1800],q=function e(n){document.prerendering?h((function(){return e(n)})):"complete"!==document.readyState?addEventListener("load",(function(){return e(n)}),!0):setTimeout(n,0)},_=function(e,n){n=n||{};var o=a("TTFB"),u=c(e,o,V,n.reportAllChanges);q((function(){var s=i();if(s){var f=s.responseStart;if(f<=0||f>performance.now())return;o.value=Math.max(f-r(),0),o.entries=[s],u(!0),t((function(){o=a("TTFB",0),(u=c(e,o,V,n.reportAllChanges))(!0)}))}}))};export{T as CLSThresholds,g as FCPThresholds,b as FIDThresholds,F as INPThresholds,R as LCPThresholds,V as TTFBThresholds,w as getCLS,y as getFCP,C as getFID,H as getINP,O as getLCP,_ as getTTFB,w as onCLS,y as onFCP,C as onFID,H as onINP,O as onLCP,_ as onTTFB};
