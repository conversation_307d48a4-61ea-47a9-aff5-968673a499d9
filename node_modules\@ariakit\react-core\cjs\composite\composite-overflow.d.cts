import type { PopoverOptions } from "../popover/popover.js";
import type { As, Props } from "../utils/types.js";
import type { CompositeOverflowStore } from "./composite-overflow-store.js";
/**
 * Returns props to create a `CompositeOverflow` component.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const store = useCompositeOverflowStore();
 * const props = useCompositeOverflow({ store });
 * <Role {...props}>
 *   <CompositeItem>Item 3</CompositeItem>
 *   <CompositeItem>Item 4</CompositeItem>
 * </Role>
 * ```
 */
export declare const useCompositeOverflow: import("../utils/types.js").Hook<CompositeOverflowOptions<"div">>;
/**
 * Renders a popover that will contain the overflow items in a composite
 * collection.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const composite = useCompositeStore();
 * const overflow = useCompositeOverflowStore();
 * <Composite store={composite}>
 *   <CompositeItem>Item 1</CompositeItem>
 *   <CompositeItem>Item 2</CompositeItem>
 *   <CompositeOverflowDisclosure store={overflow}>
 *     +2 items
 *   </CompositeOverflowDisclosure>
 *   <CompositeOverflow store={overflow}>
 *     <CompositeItem>Item 3</CompositeItem>
 *     <CompositeItem>Item 4</CompositeItem>
 *   </CompositeOverflow>
 * </Composite>
 * ```
 */
export declare const CompositeOverflow: import("../utils/types.js").Component<CompositeOverflowOptions<"div">>;
export interface CompositeOverflowOptions<T extends As = "div"> extends PopoverOptions<T> {
    /**
     * Object returned by the `useCompositeOverflowStore` hook.
     */
    store: CompositeOverflowStore;
}
export type CompositeOverflowProps<T extends As = "div"> = Props<CompositeOverflowOptions<T>>;
