:root {
  --font-family: var(--font-family-body, sans-serif);
  --font-size: var(--font-size-body, 16px);
  --button-color: var(--color-grayscale-700, #333);
  --button-background: transparent;
  --button-background-hover: var(--color-primary-50, #e3f2fd);
  --button-background-active: var(--color-primary-100, #bbdefb);
  --button-border-focus: var(--color-primary-700, #1976d2);
  --combobox-background: var(--color-grayscale-50, #eee);
  --combobox-color: var(--color-grayscale-700, #333);
  --combobox-border: transparent;
  --combobox-border-focus: var(--color-primary-700, #1976d2);
  --grid-background: var(--color-background-200, #fff);
  --grid-color: var(--color-grayscale-700, #333);
  --grid-shadow-50: var(--color-shadow-50, rgba(0, 0, 0, 0.05));
  --grid-shadow-100: var(--color-shadow-50, rgba(0, 0, 0, 0.1));
  --grid-title-color: var(--color-grayscale-400, #999);
  --gridcell-background-hover: var(--color-primary-50, #e3f2fd);
  --gridcell-background-focus: var(--color-primary-100, #bbdefb);
  --footer-color: var(--color-grayscale-600, #666);
}

button {
  display: inline-flex;
  align-items: center;
  font-family: var(--font-family);
  background-color: var(--button-background);
  color: var(--button-color);
  font-size: var(--font-size);
  position: relative;
  border: 0;
  padding: 0 1em;
  line-height: 2.5em;
  border-radius: 4px;
  cursor: pointer;
  outline: 0;
}

button svg {
  fill: currentColor;
  width: 1.5em;
  height: 1.5em;
  margin-left: 0.25em;
  margin-right: -0.25em;
}

button:hover {
  background-color: var(--button-background-hover);
}

button[aria-expanded="true"],
button:active {
  background-color: var(--button-background-active);
}

button:focus {
  box-shadow: 0 0 0 2px var(--button-border-focus);
}

[role="dialog"] {
  display: flex;
  flex-direction: column;
  font-family: var(--font-family);
  font-size: var(--font-size);
  background-color: var(--grid-background);
  color: var(--grid-color);
  width: 400px;
  max-height: calc(100% - 100px);
  z-index: 999;
  padding: 1em;
  box-sizing: border-box;
  border-radius: 4px;
  outline: none;
  box-shadow:
    0 0 8px var(--grid-shadow-50),
    0 10px 10px -5px var(--grid-shadow-50),
    0 20px 25px -5px var(--grid-shadow-100);
}

[role="combobox"] {
  flex: none;
  font-family: var(--font-family);
  font-size: var(--font-size);
  background-color: var(--combobox-background);
  color: var(--combobox-color);
  border: 1px solid var(--combobox-border);
  border-radius: 4px;
  height: 2.5em;
  padding: 0 1em;
  outline: 0;
  width: 100%;
  box-sizing: border-box;
}

[role="combobox"]:focus {
  border-color: var(--combobox-border-focus);
  box-shadow: 0 0 0 1px var(--combobox-border-focus);
}

[role="grid"] {
  flex: 1;
  color: var(--grid-color);
  margin: 1em -1em;
  padding: 0 1em;
  overflow-y: auto;
  box-sizing: border-box;
}

[role="grid"] h3 {
  font-size: 0.9em;
  text-transform: uppercase;
  font-weight: 500;
  color: var(--grid-title-color);
}

[role="gridcell"] {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  cursor: default;
  padding: 1.5em 1em;
  border-radius: 4px;
  font-size: 0.9em;
  width: calc(100% / 3);
  box-sizing: border-box;
  text-align: center;
}

[role="gridcell"] svg {
  width: 1.5em;
  margin-bottom: 1em;
  fill: currentColor;
}

[role="gridcell"]:hover {
  background-color: var(--gridcell-background-hover);
}

[role="combobox"]:focus + [role="grid"] [aria-selected="true"] {
  background-color: var(--gridcell-background-focus);
}

[role="dialog"] footer {
  padding: 1em;
  font-size: 0.85em;
  color: var(--footer-color);
}
