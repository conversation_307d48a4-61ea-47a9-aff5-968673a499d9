"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _BFE5R4EZcjs = require('../__chunks/BFE5R4EZ.cjs');


var _MI6NUQYQcjs = require('../__chunks/MI6NUQYQ.cjs');





var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');
require('../__chunks/5F4DVUNS.cjs');
require('../__chunks/ULSPM3Y3.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/tab/tab-store.ts
function createTabStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const composite = _BFE5R4EZcjs.createCompositeStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    orientation: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "horizontal"
    ),
    focusLoop: _KBNYGXWIcjs.defaultValue.call(void 0, props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
  const panels = _MI6NUQYQcjs.createCollectionStore.call(void 0, );
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite.getState()), {
    selectedId: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.selectedId,
      syncState == null ? void 0 : syncState.selectedId,
      props.defaultSelectedId,
      void 0
    ),
    selectOnMove: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.selectOnMove,
      syncState == null ? void 0 : syncState.selectOnMove,
      true
    )
  });
  const tab = _F6HPKLO2cjs.createStore.call(void 0, initialState, composite, props.store);
  _F6HPKLO2cjs.setup.call(void 0, 
    tab,
    () => _F6HPKLO2cjs.sync.call(void 0, tab, ["moves"], () => {
      const { activeId, selectOnMove } = tab.getState();
      if (!selectOnMove)
        return;
      if (!activeId)
        return;
      const tabItem = composite.item(activeId);
      if (!tabItem)
        return;
      if (tabItem.dimmed)
        return;
      if (tabItem.disabled)
        return;
      tab.setState("selectedId", tabItem.id);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    tab,
    () => _F6HPKLO2cjs.batch.call(void 0, 
      tab,
      ["selectedId"],
      (state) => tab.setState("activeId", state.selectedId)
    )
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    tab,
    () => _F6HPKLO2cjs.sync.call(void 0, tab, ["selectedId", "renderedItems"], (state) => {
      if (state.selectedId !== void 0)
        return;
      const { activeId, renderedItems } = tab.getState();
      const tabItem = composite.item(activeId);
      if (tabItem && !tabItem.disabled && !tabItem.dimmed) {
        tab.setState("selectedId", tabItem.id);
      } else {
        const tabItem2 = renderedItems.find(
          (item) => !item.disabled && !item.dimmed
        );
        tab.setState("selectedId", tabItem2 == null ? void 0 : tabItem2.id);
      }
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    tab,
    () => _F6HPKLO2cjs.sync.call(void 0, tab, ["renderedItems"], (state) => {
      const tabs = state.renderedItems;
      if (!tabs.length)
        return;
      return _F6HPKLO2cjs.sync.call(void 0, panels, ["renderedItems"], (state2) => {
        const items = state2.renderedItems;
        const hasOrphanPanels = items.some((panel) => !panel.tabId);
        if (!hasOrphanPanels)
          return;
        items.forEach((panel, i) => {
          if (panel.tabId)
            return;
          const tabItem = tabs[i];
          if (!tabItem)
            return;
          panels.renderItem(_AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, panel), { tabId: tabItem.id }));
        });
      });
    })
  );
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite), tab), {
    panels,
    setSelectedId: (id) => tab.setState("selectedId", id),
    select: (id) => {
      tab.setState("selectedId", id);
      composite.move(id);
    }
  });
}


exports.createTabStore = createTabStore;
