"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _CDNJPIEGcjs = require('../__chunks/CDNJPIEG.cjs');


var _BZTDJIVTcjs = require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/OLOZ5JT2.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');
require('../__chunks/AV6KTKLE.cjs');

// src/collection/collection-provider.tsx
var _jsxruntime = require('react/jsx-runtime');
function CollectionProvider(props = {}) {
  const store = _CDNJPIEGcjs.useCollectionStore.call(void 0, props);
  return /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _BZTDJIVTcjs.CollectionContextProvider, { value: store, children: props.children });
}


exports.CollectionProvider = CollectionProvider;
