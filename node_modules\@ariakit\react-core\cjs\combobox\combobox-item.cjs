"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _IPYAEPOTcjs = require('../__chunks/IPYAEPOT.cjs');




var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');


var _IO6J4PANcjs = require('../__chunks/IO6J4PAN.cjs');
require('../__chunks/DAJUUBUI.cjs');
require('../__chunks/R5A2WTWB.cjs');
require('../__chunks/6ZZYUFPW.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/OLOZ5JT2.cjs');




var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-item.tsx
var _react = require('react');
var _dom = require('@ariakit/core/utils/dom');
var _events = require('@ariakit/core/utils/events');
var _focus = require('@ariakit/core/utils/focus');
var _misc = require('@ariakit/core/utils/misc');
var _jsxruntime = require('react/jsx-runtime');
function isSelected(storeValue, itemValue) {
  if (itemValue == null)
    return;
  if (storeValue == null)
    return false;
  if (Array.isArray(storeValue)) {
    return storeValue.includes(itemValue);
  }
  return storeValue === itemValue;
}
var useComboboxItem = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, {
      store,
      value,
      hideOnClick,
      selectValueOnClick = true,
      setValueOnClick,
      focusOnHover = false,
      moveOnKeyPress = true,
      getItem: getItemProp
    } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
      "store",
      "value",
      "hideOnClick",
      "selectValueOnClick",
      "setValueOnClick",
      "focusOnHover",
      "moveOnKeyPress",
      "getItem"
    ]);
    const context = _3WCBE6SUcjs.useComboboxScopedContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxItem must be wrapped in a ComboboxList or ComboboxPopover component."
    );
    const getItem = _react.useCallback.call(void 0, 
      (item) => {
        const nextItem = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, item), { value });
        if (getItemProp) {
          return getItemProp(nextItem);
        }
        return nextItem;
      },
      [value, getItemProp]
    );
    const multiSelectable = store.useState(
      (state) => Array.isArray(state.selectedValue)
    );
    setValueOnClick = setValueOnClick != null ? setValueOnClick : !multiSelectable;
    hideOnClick = hideOnClick != null ? hideOnClick : value != null && !multiSelectable;
    const onClickProp = props.onClick;
    const setValueOnClickProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, setValueOnClick);
    const selectValueOnClickProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, selectValueOnClick);
    const hideOnClickProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, hideOnClick);
    const onClick = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onClickProp == null ? void 0 : onClickProp(event);
      if (event.defaultPrevented)
        return;
      if (_events.isDownloading.call(void 0, event))
        return;
      if (_events.isOpeningInNewTab.call(void 0, event))
        return;
      if (value != null) {
        if (selectValueOnClickProp(event)) {
          store == null ? void 0 : store.setSelectedValue((prevValue) => {
            if (!Array.isArray(prevValue))
              return value;
            if (prevValue.includes(value)) {
              return prevValue.filter((v) => v !== value);
            }
            return [...prevValue, value];
          });
        }
        if (setValueOnClickProp(event)) {
          store == null ? void 0 : store.setValue(value);
        }
      }
      if (hideOnClickProp(event)) {
        store == null ? void 0 : store.move(null);
        store == null ? void 0 : store.hide();
      }
    });
    const onKeyDownProp = props.onKeyDown;
    const onKeyDown = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onKeyDownProp == null ? void 0 : onKeyDownProp(event);
      if (event.defaultPrevented)
        return;
      const baseElement = store == null ? void 0 : store.getState().baseElement;
      if (!baseElement)
        return;
      if (_focus.hasFocus.call(void 0, baseElement))
        return;
      const printable = event.key.length === 1;
      if (printable || event.key === "Backspace" || event.key === "Delete") {
        queueMicrotask(() => baseElement.focus());
        if (_dom.isTextField.call(void 0, baseElement)) {
          store == null ? void 0 : store.setValue(baseElement.value);
        }
      }
    });
    const selected = store.useState(
      (state) => isSelected(state.selectedValue, value)
    );
    if (multiSelectable && selected != null) {
      props = _AV6KTKLEcjs.__spreadValues.call(void 0, {
        "aria-selected": selected
      }, props);
    }
    props = _EO6LS72Hcjs.useWrapElement.call(void 0, 
      props,
      (element) => /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _3WCBE6SUcjs.ComboboxItemValueContext.Provider, { value, children: /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _3WCBE6SUcjs.ComboboxItemCheckedContext.Provider, { value: selected != null ? selected : false, children: element }) }),
      [value, selected]
    );
    const contentElement = store.useState("contentElement");
    props = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      role: _dom.getPopupItemRole.call(void 0, contentElement),
      children: value
    }, props), {
      onClick,
      onKeyDown
    });
    const moveOnKeyPressProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, moveOnKeyPress);
    props = _IO6J4PANcjs.useCompositeItem.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      store
    }, props), {
      getItem,
      // Dispatch a custom event on the combobox input when moving to an item
      // with the keyboard so the Combobox component can enable inline
      // autocompletion.
      moveOnKeyPress: (event) => {
        if (!moveOnKeyPressProp(event))
          return false;
        const moveEvent = new Event("combobox-item-move");
        const baseElement = store == null ? void 0 : store.getState().baseElement;
        baseElement == null ? void 0 : baseElement.dispatchEvent(moveEvent);
        return true;
      }
    }));
    props = _IPYAEPOTcjs.useCompositeHover.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, { store, focusOnHover }, props));
    return props;
  }
);
var ComboboxItem = _RNZNGEL4cjs.createMemoComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxItem(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "div", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxItem.displayName = "ComboboxItem";
}



exports.ComboboxItem = ComboboxItem; exports.useComboboxItem = useComboboxItem;
