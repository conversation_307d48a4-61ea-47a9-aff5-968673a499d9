"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');


var _F34KLJKGcjs = require('../__chunks/F34KLJKG.cjs');
require('../__chunks/QOD2KY3N.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-separator.ts
var _misc = require('@ariakit/core/utils/misc');
var useComboboxSeparator = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store"]);
    const context = _3WCBE6SUcjs.useComboboxScopedContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxSeparator must be wrapped in a ComboboxList or ComboboxPopover component."
    );
    props = _F34KLJKGcjs.useCompositeSeparator.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, { store }, props));
    return props;
  }
);
var ComboboxSeparator = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxSeparator(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "hr", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxSeparator.displayName = "ComboboxSeparator";
}



exports.ComboboxSeparator = ComboboxSeparator; exports.useComboboxSeparator = useComboboxSeparator;
