import type { KeyboardEvent } from "react";
import type { BooleanOrCallback } from "@ariakit/core/utils/types";
import type { CollectionItemOptions } from "../collection/collection-item.js";
import type { CommandOptions } from "../command/command.js";
import type { As, Props } from "../utils/types.js";
import type { CompositeStore } from "./composite-store.js";
/**
 * Returns props to create a `CompositeItem` component.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const store = useCompositeStore();
 * const props = useCompositeItem({ store });
 * <Role {...props}>Item 1</Role>
 * ```
 */
export declare const useCompositeItem: import("../utils/types.js").Hook<CompositeItemOptions<"button">>;
/**
 * Renders a focusable item as part of a composite widget. The `tabindex`
 * attribute is automatically managed by this component based on the
 * [`virtualFocus`](https://ariakit.org/reference/composite-provider#virtualfocus)
 * option.
 *
 * When this component receives DOM focus or is virtually focused (when the
 * [`virtualFocus`](https://ariakit.org/reference/composite-provider#virtualfocus)
 * option is set to `true`), the element will automatically receive the
 * [`data-active-item`](https://ariakit.org/guide/styling#data-active-item)
 * attribute. This can be used to style the focused item, no matter the focus
 * approach employed.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx {3-5}
 * <CompositeProvider>
 *   <Composite>
 *     <CompositeItem>Item 1</CompositeItem>
 *     <CompositeItem>Item 2</CompositeItem>
 *     <CompositeItem>Item 3</CompositeItem>
 *   </Composite>
 * </CompositeProvider>
 * ```
 */
export declare const CompositeItem: import("../utils/types.js").Component<CompositeItemOptions<"button">>;
export interface CompositeItemOptions<T extends As = "button"> extends CommandOptions<T>, CollectionItemOptions<T> {
    /**
     * Object returned by the
     * [`useCompositeStore`](https://ariakit.org/reference/use-composite-store)
     * hook. If not provided, the closest
     * [`Composite`](https://ariakit.org/reference/composite) or
     * [`CompositeProvider`](https://ariakit.org/reference/composite-provider)
     * components' context will be used.
     */
    store?: CompositeStore;
    /**
     * Determines if the item should be registered as part of the collection. If
     * this is set to `false`, the item won't be accessible via arrow keys.
     *
     * Live examples:
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     */
    shouldRegisterItem?: CollectionItemOptions<T>["shouldRegisterItem"];
    /**
     * The id that will be used to group items in the same row. This is usually
     * retrieved by the
     * [`CompositeRow`](https://ariakit.org/reference/composite-row) component
     * through context so in most cases you don't need to set it manually.
     */
    rowId?: string;
    /**
     * Whether the scroll behavior should be prevented when pressing arrow keys on
     * the first or the last items.
     * @deprecated Use CSS
     * [`scroll-margin`](https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-margin)
     * instead.
     * @default false
     */
    preventScrollOnKeyDown?: BooleanOrCallback<KeyboardEvent<HTMLElement>>;
    /**
     * Determines if pressing arrow keys while this item is in focus should move
     * focus to a different item.
     *
     * **Note**: To entirely disable focus moving within a composite widget, you
     * can use the
     * [`focusOnMove`](https://ariakit.org/reference/composite#focusonmove) prop
     * on the composite component instead. If you want to control the behavior
     * _only when arrow keys are pressed_, where
     * [`focusOnMove`](https://ariakit.org/reference/composite#focusonmove) may
     * not be applicable, this prop must be set on all composite items because
     * they each manage their own key presses, as well as on the composite
     * component itself.
     * @default true
     * @example
     * ```jsx
     * <Composite moveOnKeyPress={false}>
     *   <CompositeItem moveOnKeyPress={false} />
     *   <CompositeItem moveOnKeyPress={false} />
     * </Composite>
     * ```
     */
    moveOnKeyPress?: BooleanOrCallback<KeyboardEvent<HTMLElement>>;
    /**
     * When the `tabbable` prop is set to `true`, the [roving
     * tabindex](https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_roving_tabindex)
     * method is partially disabled for this element. This means that the
     * `tabIndex` prop won't be assigned `-1` when the item is inactive. In
     * addition to using arrow keys, users will be able to tab to this element,
     * leading to the composite widget no longer existing as a single tab stop.
     *
     * As per the [ARIA spec](https://w3c.github.io/aria/#composite):
     *
     * > Authors **SHOULD** ensure that a composite widget exists as a single
     * > navigation stop within the larger navigation system of the web page.
     *
     * Additionally, as stated in
     * [RFC-2119](https://www.rfc-editor.org/rfc/rfc2119.txt):
     *
     * > **SHOULD** This word, or the adjective "RECOMMENDED", mean that there may
     * > exist valid reasons in particular circumstances to ignore a particular
     * > item, but the full implications must be understood and carefully weighed
     * > before choosing a different course.
     *
     * Therefore, while this may be allowed, you should think carefully about the
     * implications of using this prop.
     *
     * **Note**: This prop has no effect when the
     * [`virtualFocus`](https://ariakit.org/reference/composite-provider#virtualfocus)
     * option is enabled.
     *
     * Live examples:
     * - [Navigation Menubar](https://ariakit.org/examples/menubar-navigation)
     */
    tabbable?: boolean;
}
export type CompositeItemProps<T extends As = "button"> = Props<CompositeItemOptions<T>>;
