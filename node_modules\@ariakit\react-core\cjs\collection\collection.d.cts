import type { As, Options, Props } from "../utils/types.js";
import type { CollectionStore } from "./collection-store.js";
/**
 * Returns props to create a `Collection` component. It receives the collection
 * store through the `store` prop and provides context for `CollectionItem`
 * components.
 * @see https://ariakit.org/components/collection
 * @example
 * ```jsx
 * const collection = useCollectionStore();
 * const props = useCollection({ store });
 * <Role {...props}>
 *   <CollectionItem>Item 1</CollectionItem>
 *   <CollectionItem>Item 2</CollectionItem>
 *   <CollectionItem>Item 3</CollectionItem>
 * </Role>
 * ```
 */
export declare const useCollection: import("../utils/types.js").Hook<CollectionOptions<"div">>;
/**
 * Renders a simple wrapper for collection items. It accepts a collection store
 * through the [`store`](https://ariakit.org/reference/collection#store) prop
 * and provides context for
 * [`CollectionItem`](https://ariakit.org/reference/collection-item) components.
 * @see https://ariakit.org/components/collection
 * @example
 * ```jsx
 * const collection = useCollectionStore();
 * <Collection store={collection}>
 *   <CollectionItem>Item 1</CollectionItem>
 *   <CollectionItem>Item 2</CollectionItem>
 *   <CollectionItem>Item 3</CollectionItem>
 * </Collection>
 * ```
 */
export declare const Collection: import("../utils/types.js").Component<CollectionOptions<"div">>;
export interface CollectionOptions<T extends As = "div"> extends Options<T> {
    /**
     * Object returned by the
     * [`useCollectionStore`](https://ariakit.org/reference/use-collection-store)
     * hook. If not provided, the closest
     * [`CollectionProvider`](https://ariakit.org/reference/collection-provider)
     * component's context will be used.
     */
    store?: CollectionStore;
}
export type CollectionProps<T extends As = "div"> = Props<CollectionOptions<T>>;
