import type { ComboboxStore } from "../combobox/combobox-store.js";
import type { CompositeStoreFunctions, CompositeStoreOptions, CompositeStoreState } from "../composite/composite-store.js";
import type { HovercardStoreFunctions, HovercardStoreOptions, HovercardStoreState } from "../hovercard/hovercard-store.js";
import type { Store, StoreOptions, StoreProps } from "../utils/store.js";
import type { BivariantCallback, PickRequired, SetState, SetStateAction } from "../utils/types.js";
import type { MenuBarStore } from "./menu-bar-store.js";
export declare function createMenuStore<T extends MenuStoreValues = MenuStoreValues>(props: PickRequired<MenuStoreProps<T>, "values" | "defaultValues">): MenuStore<T>;
export declare function createMenuStore(props?: MenuStoreProps): MenuStore;
export type MenuStoreValues = Record<string, string | boolean | number | Array<string | number>>;
export interface MenuStoreState<T extends MenuStoreValues = MenuStoreValues> extends CompositeStoreState, HovercardStoreState {
    /**
     * Determines the element that should be focused when the menu is opened.
     */
    initialFocus: "container" | "first" | "last";
    /**
     * A map of names and values that will be used by the
     * [`MenuItemCheckbox`](https://ariakit.org/reference/menu-item-checkbox) and
     * [`MenuItemRadio`](https://ariakit.org/reference/menu-item-radio)
     * components.
     *
     * Live examples:
     * - [MenuItemCheckbox](https://ariakit.org/examples/menu-item-checkbox)
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     */
    values: T;
    /** @default "vertical" */
    orientation: CompositeStoreState["orientation"];
    /** @default "bottom-start" */
    placement: HovercardStoreState["placement"];
    /** @default 0 */
    hideTimeout?: HovercardStoreState["hideTimeout"];
}
export interface MenuStoreFunctions<T extends MenuStoreValues = MenuStoreValues> extends CompositeStoreFunctions, HovercardStoreFunctions, Pick<MenuStoreOptions, "combobox" | "parent" | "menubar"> {
    /**
     * Hides the menu and all its parent menus.
     *
     * Live examples:
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     */
    hideAll: () => void;
    /**
     * Sets the `initialFocus` state.
     */
    setInitialFocus: SetState<MenuStoreState<T>["initialFocus"]>;
    /**
     * Sets the [`values`](https://ariakit.org/reference/menu-provider#values)
     * state.
     * @example
     * store.setValues({ watching: ["issues"] });
     * store.setValues((values) => ({ ...values, watching: ["issues"] }));
     */
    setValues: SetState<MenuStoreState<T>["values"]>;
    /**
     * Sets a specific menu value.
     *
     * Live examples:
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     * @example
     * store.setValue("watching", ["issues"]);
     * store.setValue("watching", (value) => [...value, "issues"]);
     */
    setValue: BivariantCallback<(name: string, value: SetStateAction<MenuStoreState<T>["values"][string]>) => void>;
}
export interface MenuStoreOptions<T extends MenuStoreValues = MenuStoreValues> extends CompositeStoreOptions, HovercardStoreOptions, StoreOptions<MenuStoreState<T>, "orientation" | "placement" | "hideTimeout" | "values"> {
    /**
     * A reference to a combobox store. This is used when combining the combobox
     * with a menu (e.g., dropdown menu with a search input). The stores will
     * share the same state.
     */
    combobox?: ComboboxStore | null;
    /**
     * A reference to a parent menu store. This is used on nested menus.
     */
    parent?: MenuStore | null;
    /**
     * A reference to a menubar store. This is used when rendering menus inside a
     * menubar.
     */
    menubar?: MenuBarStore | null;
    /**
     * The default values for the
     * [`values`](https://ariakit.org/reference/menu-provider#values) state.
     *
     * Live examples:
     * - [MenuItemCheckbox](https://ariakit.org/examples/menu-item-checkbox)
     * - [MenuItemRadio](https://ariakit.org/examples/menu-item-radio)
     * @default {}
     */
    defaultValues?: MenuStoreState<T>["values"];
}
export interface MenuStoreProps<T extends MenuStoreValues = MenuStoreValues> extends MenuStoreOptions<T>, StoreProps<MenuStoreState<T>> {
}
export interface MenuStore<T extends MenuStoreValues = MenuStoreValues> extends MenuStoreFunctions<T>, Store<MenuStoreState<T>> {
}
