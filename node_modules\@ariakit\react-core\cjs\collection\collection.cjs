"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";



var _BZTDJIVTcjs = require('../__chunks/BZTDJIVT.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');


var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');


var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/collection/collection.tsx
var _jsxruntime = require('react/jsx-runtime');
var useCollection = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store"]);
    const context = _BZTDJIVTcjs.useCollectionProviderContext.call(void 0, );
    store = store || context;
    props = _EO6LS72Hcjs.useWrapElement.call(void 0, 
      props,
      (element) => /* @__PURE__ */ _jsxruntime.jsx.call(void 0, _BZTDJIVTcjs.CollectionScopedContextProvider, { value: store, children: element }),
      [store]
    );
    return props;
  }
);
var Collection = _RNZNGEL4cjs.createComponent.call(void 0, (props) => {
  const htmlProps = useCollection(props);
  return _RNZNGEL4cjs.createElement.call(void 0, "div", htmlProps);
});
if (process.env.NODE_ENV !== "production") {
  Collection.displayName = "Collection";
}



exports.Collection = Collection; exports.useCollection = useCollection;
