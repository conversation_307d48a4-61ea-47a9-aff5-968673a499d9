"use client";
import {
  canUseDOM,
  closest,
  contains,
  getActiveElement,
  getDocument,
  getPopupItemRole,
  getPopupRole,
  getScrollingElement,
  getTextboxSelection,
  getWindow,
  isButton,
  isFrame,
  isPartiallyHidden,
  isTextField,
  isVisible,
  matches,
  scrollIntoViewIfNeeded,
  setSelectionRange
} from "../__chunks/DLOEKDPY.js";
import "../__chunks/4R3V3JGP.js";
export {
  canUseDOM,
  closest,
  contains,
  getActiveElement,
  getDocument,
  getPopupItemRole,
  getPopupRole,
  getScrollingElement,
  getTextboxSelection,
  getWindow,
  isButton,
  isFrame,
  isPartiallyHidden,
  isTextField,
  isVisible,
  matches,
  scrollIntoViewIfNeeded,
  setSelectionRange
};
