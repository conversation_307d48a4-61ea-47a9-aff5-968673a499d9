"use client";
import {
  createCompositeStore
} from "../__chunks/IERTEJ3A.js";
import "../__chunks/22K762VQ.js";
import "../__chunks/EAHJFCU4.js";
import {
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import "../__chunks/DLOEKDPY.js";
import "../__chunks/7PRQYBBV.js";
import {
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/toolbar/toolbar-store.ts
function createToolbarStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  return createCompositeStore(__spreadProps(__spreadValues({}, props), {
    orientation: defaultValue(
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "horizontal"
    ),
    focusLoop: defaultValue(props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
}
export {
  createToolbarStore
};
