declare type AsRef = {
    $$valtioRef: true;
};
declare type Path = (string | symbol)[];
declare type Op = [op: 'set', path: Path, value: unknown, prevValue: unknown] | [op: 'delete', path: Path, prevValue: unknown] | [op: 'resolve', path: Path, value: unknown] | [op: 'reject', path: Path, error: unknown];
declare type AnyFunction = (...args: any[]) => any;
/**
 * This is not a public API.
 * It can be changed without any notice.
 */
export declare type INTERNAL_Snapshot<T> = T extends AnyFunction ? T : T extends AsRef ? T : T extends Promise<infer V> ? INTERNAL_Snapshot<V> : {
    readonly [K in keyof T]: INTERNAL_Snapshot<T[K]>;
};
export declare function proxy<T extends object>(initialObject?: T): T;
export declare function getVersion(proxyObject: unknown): number | undefined;
export declare function subscribe<T extends object>(proxyObject: T, callback: (ops: Op[]) => void, notifyInSync?: boolean): () => void;
export declare function snapshot<T extends object>(proxyObject: T): INTERNAL_Snapshot<T>;
export declare function ref<T extends object>(obj: T): T & AsRef;
export declare const unstable_buildProxyFunction: (objectIs?: (value1: any, value2: any) => boolean, newProxy?: <T extends object>(target: T, handler: ProxyHandler<T>) => T, canProxy?: (x: unknown) => boolean, PROMISE_RESULT?: symbol, PROMISE_ERROR?: symbol, snapshotCache?: WeakMap<object, [version: number, snapshot: unknown]>, createSnapshot?: <T_1 extends object>(version: number, target: T_1, receiver: any) => T_1, proxyCache?: WeakMap<object, object>, versionHolder?: [number], proxyFunction?: <T_2 extends object>(initialObject: T_2) => T_2) => readonly [<T_2 extends object>(initialObject: T_2) => T_2, WeakSet<object>, symbol, symbol, symbol, (value1: any, value2: any) => boolean, <T extends object>(target: T, handler: ProxyHandler<T>) => T, (x: unknown) => boolean, symbol, symbol, WeakMap<object, [version: number, snapshot: unknown]>, <T_1 extends object>(version: number, target: T_1, receiver: any) => T_1, WeakMap<object, object>, [number]];
export {};
