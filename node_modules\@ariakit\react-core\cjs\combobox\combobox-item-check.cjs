"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _T4KYZJUYcjs = require('../__chunks/T4KYZJUY.cjs');
require('../__chunks/ERFHNHON.cjs');


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-item-check.ts
var _react = require('react');
var useComboboxItemCheck = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store, checked } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store", "checked"]);
    const context = _react.useContext.call(void 0, _3WCBE6SUcjs.ComboboxItemCheckedContext);
    checked = checked != null ? checked : context;
    props = _T4KYZJUYcjs.useCheckboxCheck.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), { checked }));
    return props;
  }
);
var ComboboxItemCheck = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxItemCheck(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "span", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxItemCheck.displayName = "ComboboxItemCheck";
}



exports.ComboboxItemCheck = ComboboxItemCheck; exports.useComboboxItemCheck = useComboboxItemCheck;
