"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _D4TG5DM4cjs = require('../__chunks/D4TG5DM4.cjs');


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');


var _OUEE5HOScjs = require('../__chunks/OUEE5HOS.cjs');


var _ZL5DC555cjs = require('../__chunks/ZL5DC555.cjs');
require('../__chunks/WZ3DRKGP.cjs');
require('../__chunks/KSPMHEYZ.cjs');
require('../__chunks/PZL34OVO.cjs');
require('../__chunks/344F3DYO.cjs');
require('../__chunks/KREY6HXD.cjs');
require('../__chunks/5ZOCN23X.cjs');
require('../__chunks/JVDUGICD.cjs');
require('../__chunks/VV6WA3I6.cjs');
require('../__chunks/7YLCVXZ7.cjs');
require('../__chunks/5GTNIPQ6.cjs');
require('../__chunks/7TN63K2T.cjs');
require('../__chunks/V24PR4PW.cjs');
require('../__chunks/6IUEXB4L.cjs');
require('../__chunks/JF225FQ5.cjs');
require('../__chunks/7566TIRW.cjs');
require('../__chunks/J3OG6T3B.cjs');
require('../__chunks/65LGW5LY.cjs');
require('../__chunks/XB3G2EO2.cjs');
require('../__chunks/CVD2AZE2.cjs');
require('../__chunks/2BIO7R5N.cjs');
require('../__chunks/LAUATD5O.cjs');
require('../__chunks/YPVQYY4J.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');
require('../__chunks/R66IWXK6.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/W5LJEMLB.cjs');
require('../__chunks/JAQJG42R.cjs');
require('../__chunks/OLOZ5JT2.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-popover.tsx
var _dom = require('@ariakit/core/utils/dom');
var _misc = require('@ariakit/core/utils/misc');
function isController(target, ...ids) {
  if (!target)
    return false;
  if ("id" in target) {
    const selector = ids.filter(Boolean).map((id) => `[aria-controls~="${id}"]`).join(", ");
    if (!selector)
      return false;
    return _dom.matches.call(void 0, target, selector);
  }
  return false;
}
var useComboboxPopover = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, {
      store,
      modal,
      tabIndex,
      alwaysVisible,
      hideOnInteractOutside = true
    } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
      "store",
      "modal",
      "tabIndex",
      "alwaysVisible",
      "hideOnInteractOutside"
    ]);
    const context = _3WCBE6SUcjs.useComboboxProviderContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxPopover must receive a `store` prop or be wrapped in a ComboboxProvider component."
    );
    const baseElement = store.useState("baseElement");
    props = _D4TG5DM4cjs.useComboboxList.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, { store, alwaysVisible }, props));
    props = _OUEE5HOScjs.usePopover.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      store,
      modal,
      alwaysVisible,
      backdrop: false,
      autoFocusOnShow: false,
      autoFocusOnHide: false,
      finalFocus: baseElement,
      preserveTabOrderAnchor: null
    }, props), {
      // When the combobox popover is modal, we make sure to include the
      // combobox input and all the combobox controls (cancel, disclosure) in
      // the list of persistent elements so they make part of the modal context,
      // allowing users to tab through them.
      getPersistentElements() {
        var _a2;
        const elements = ((_a2 = props.getPersistentElements) == null ? void 0 : _a2.call(props)) || [];
        if (!modal)
          return elements;
        if (!store)
          return elements;
        const { contentElement, baseElement: baseElement2 } = store.getState();
        if (!baseElement2)
          return elements;
        const doc = _dom.getDocument.call(void 0, baseElement2);
        const selectors = [];
        if (contentElement == null ? void 0 : contentElement.id) {
          selectors.push(`[aria-controls~="${contentElement.id}"]`);
        }
        if (baseElement2 == null ? void 0 : baseElement2.id) {
          selectors.push(`[aria-controls~="${baseElement2.id}"]`);
        }
        if (!selectors.length)
          return [...elements, baseElement2];
        const selector = selectors.join(",");
        const controlElements = doc.querySelectorAll(selector);
        return [...elements, ...controlElements];
      },
      // Make sure we don't hide the popover when the user interacts with the
      // combobox cancel or the combobox disclosure buttons. They will have the
      // aria-controls attribute pointing to either the combobox input or the
      // combobox popover elements.
      hideOnInteractOutside(event) {
        var _a2, _b2;
        const state = store == null ? void 0 : store.getState();
        const contentId = (_a2 = state == null ? void 0 : state.contentElement) == null ? void 0 : _a2.id;
        const baseId = (_b2 = state == null ? void 0 : state.baseElement) == null ? void 0 : _b2.id;
        if (isController(event.target, contentId, baseId))
          return false;
        const result = typeof hideOnInteractOutside === "function" ? hideOnInteractOutside(event) : hideOnInteractOutside;
        return result;
      }
    }));
    return props;
  }
);
var ComboboxPopover = _ZL5DC555cjs.createDialogComponent.call(void 0, 
  _RNZNGEL4cjs.createComponent.call(void 0, (props) => {
    const htmlProps = useComboboxPopover(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "div", htmlProps);
  }),
  _3WCBE6SUcjs.useComboboxProviderContext
);
if (process.env.NODE_ENV !== "production") {
  ComboboxPopover.displayName = "ComboboxPopover";
}



exports.ComboboxPopover = ComboboxPopover; exports.useComboboxPopover = useComboboxPopover;
