"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-label.ts
var _misc = require('@ariakit/core/utils/misc');
var useComboboxLabel = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store"]);
    const context = _3WCBE6SUcjs.useComboboxProviderContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxLabel must receive a `store` prop or be wrapped in a ComboboxProvider component."
    );
    const comboboxId = store.useState((state) => {
      var _a2;
      return (_a2 = state.baseElement) == null ? void 0 : _a2.id;
    });
    props = _AV6KTKLEcjs.__spreadValues.call(void 0, {
      htmlFor: comboboxId
    }, props);
    return props;
  }
);
var ComboboxLabel = _RNZNGEL4cjs.createMemoComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxLabel(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "label", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxLabel.displayName = "ComboboxLabel";
}



exports.ComboboxLabel = ComboboxLabel; exports.useComboboxLabel = useComboboxLabel;
