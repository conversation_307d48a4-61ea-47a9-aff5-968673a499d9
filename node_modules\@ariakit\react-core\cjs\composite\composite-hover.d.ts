import type { MouseEvent as ReactMouse<PERSON><PERSON> } from "react";
import type { BooleanOrCallback } from "@ariakit/core/utils/types";
import type { As, Options, Props } from "../utils/types.js";
import type { CompositeStore } from "./composite-store.js";
/**
 * Returns props to create a `CompositeHover` component. The composite item that
 * receives these props will get focused on mouse move and lose focus to the
 * composite base element on mouse leave. This should be combined with the
 * `CompositeItem` component, the `useCompositeItem` hook or any component/hook
 * that uses them underneath.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const store = useCompositeStore();
 * const props = useCompositeHover({ store });
 * <CompositeItem store={store} {...props}>Item</CompositeItem>
 * ```
 */
export declare const useCompositeHover: import("../utils/types.js").Hook<CompositeHoverOptions<"div">>;
/**
 * Renders an element in a composite widget that receives focus on mouse move
 * and loses focus to the composite base element on mouse leave.
 *
 * This should be combined with the
 * [`CompositeItem`](https://ariakit.org/reference/composite-item) component.
 * The
 * [`focusOnHover`](https://ariakit.org/reference/composite-hover#focusonhover)
 * and
 * [`blurOnHoverEnd`](https://ariakit.org/reference/composite-hover#bluronhoverend)
 * props can be used to customize the behavior.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx {3-5}
 * <CompositeProvider>
 *   <Composite>
 *     <CompositeHover render={<CompositeItem />}>
 *       Item
 *     </CompositeHover>
 *   </Composite>
 * </CompositeProvider>
 * ```
 */
export declare const CompositeHover: import("../utils/types.js").Component<CompositeHoverOptions<"div">>;
export interface CompositeHoverOptions<T extends As = "div"> extends Options<T> {
    /**
     * Object returned by the
     * [`useCompositeStore`](https://ariakit.org/reference/use-composite-store)
     * hook. If not provided, the closest
     * [`Composite`](https://ariakit.org/reference/composite) or
     * [`CompositeProvider`](https://ariakit.org/reference/composite-provider)
     * components' context will be used.
     */
    store?: CompositeStore;
    /**
     * Determines if the composite item should be _focused_ when hovered over.
     *
     * Note that the actual DOM focus will stay on the composite element. This
     * item will get the
     * [`data-active-item`](https://ariakit.org/guide/styling#data-active-item)
     * attribute so it can be styled as if it's focused.
     *
     * Live examples:
     * - [Multi-selectable
     *   Combobox](https://ariakit.org/examples/combobox-multiple)
     * - [Combobox with integrated
     *   filter](https://ariakit.org/examples/combobox-filtering-integrated)
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * - [Navigation Menubar](https://ariakit.org/examples/menubar-navigation)
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     * @default true
     */
    focusOnHover?: BooleanOrCallback<ReactMouseEvent<HTMLElement>>;
    /**
     * Determines if the composite item should lose focus when the mouse leaves.
     * By default, this is set to `true` if
     * [`focusOnHover`](https://ariakit.org/reference/composite-hover#focusonhover)
     * is `true`.
     *
     * Live examples:
     * - [Navigation Menubar](https://ariakit.org/examples/menubar-navigation)
     * - [Combobox with integrated
     *   filter](https://ariakit.org/examples/combobox-filtering-integrated)
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     * - [Combobox with tabs](https://ariakit.org/examples/combobox-tabs)
     */
    blurOnHoverEnd?: BooleanOrCallback<ReactMouseEvent<HTMLElement>>;
}
export type CompositeHoverProps<T extends As = "div"> = Props<CompositeHoverOptions<T>>;
