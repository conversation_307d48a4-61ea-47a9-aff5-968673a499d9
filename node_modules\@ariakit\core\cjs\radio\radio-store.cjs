"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _BFE5R4EZcjs = require('../__chunks/BFE5R4EZ.cjs');
require('../__chunks/MI6NUQYQ.cjs');


var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');
require('../__chunks/5F4DVUNS.cjs');
require('../__chunks/ULSPM3Y3.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/radio/radio-store.ts
function createRadioStore(_a = {}) {
  var props = _AV6KTKLEcjs.__objRest.call(void 0, _a, []);
  var _a2;
  const syncState = (_a2 = props.store) == null ? void 0 : _a2.getState();
  const composite = _BFE5R4EZcjs.createCompositeStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    focusLoop: _KBNYGXWIcjs.defaultValue.call(void 0, props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite.getState()), {
    value: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.value,
      syncState == null ? void 0 : syncState.value,
      props.defaultValue,
      null
    )
  });
  const radio = _F6HPKLO2cjs.createStore.call(void 0, initialState, composite, props.store);
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite), radio), {
    setValue: (value) => radio.setState("value", value)
  });
}


exports.createRadioStore = createRadioStore;
