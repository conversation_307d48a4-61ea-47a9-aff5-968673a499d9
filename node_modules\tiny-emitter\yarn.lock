# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@tap-format/exit@0.2.0":
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/@tap-format/exit/-/exit-0.2.0.tgz#b58736bc55d30802c012c5adfca51b47040310cd"
  dependencies:
    ramda "^0.18.0"
    rx "^4.0.7"

"@tap-format/failures@0.2.0":
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/@tap-format/failures/-/failures-0.2.0.tgz#bb6f5edc3bc3c57c62885bc7c214cc7abdfc2a07"
  dependencies:
    chalk "^1.1.1"
    diff "^2.2.1"
    figures "^1.4.0"
    ramda "^0.18.0"
    rx "^4.0.7"

"@tap-format/parser@0.2.0":
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/@tap-format/parser/-/parser-0.2.0.tgz#bdc1d95e694781157593283bb3c3fec132a3115d"
  dependencies:
    duplexer "^0.1.1"
    js-yaml "^3.4.6"
    ramda "^0.18.0"
    readable-stream "^2.0.4"
    rx "^4.0.7"
    rx-node "^1.0.1"
    split "^1.0.0"

"@tap-format/results@0.2.0":
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/@tap-format/results/-/results-0.2.0.tgz#192d64ac41f146fa2722db1c0a22ed80478f54fd"
  dependencies:
    chalk "^1.1.1"
    hirestime "^1.0.6"
    pretty-ms "^2.1.0"
    rx "^4.0.7"

"@tap-format/spec@0.2.0":
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/@tap-format/spec/-/spec-0.2.0.tgz#93f7d2f0dcefe526b4776800b9bd7f80db5aaec7"
  dependencies:
    "@tap-format/exit" "0.2.0"
    "@tap-format/failures" "0.2.0"
    "@tap-format/parser" "0.2.0"
    "@tap-format/results" "0.2.0"
    chalk "^1.1.1"
    figures "^1.4.0"
    rx "^4.0.7"

Base64@~0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/Base64/-/Base64-0.2.1.tgz#ba3a4230708e186705065e66babdd4c35cf60028"

JSONStream@^1.0.3:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/JSONStream/-/JSONStream-1.3.2.tgz#c102371b6ec3a7cf3b847ca00c20bb0fce4c6dea"
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

JSONStream@~0.6.4:
  version "0.6.4"
  resolved "https://registry.yarnpkg.com/JSONStream/-/JSONStream-0.6.4.tgz#4b2c8063f8f512787b2375f7ee9db69208fa2dcb"
  dependencies:
    jsonparse "0.0.5"
    through "~2.2.7"

JSONStream@~0.7.1:
  version "0.7.4"
  resolved "https://registry.yarnpkg.com/JSONStream/-/JSONStream-0.7.4.tgz#734290e41511eea7c2cfe151fbf9a563a97b9786"
  dependencies:
    jsonparse "0.0.5"
    through ">=2.2.7 <3"

acorn-node@^1.2.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/acorn-node/-/acorn-node-1.3.0.tgz#5f86d73346743810ef1269b901dbcbded020861b"
  dependencies:
    acorn "^5.4.1"
    xtend "^4.0.1"

acorn@^2.7.0:
  version "2.7.0"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-2.7.0.tgz#ab6e7d9d886aaca8b085bc3312b79a198433f0e7"

acorn@^4.0.3:
  version "4.0.13"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-4.0.13.tgz#105495ae5361d697bd195c825192e1ad7f253787"

acorn@^5.2.1, acorn@^5.4.1:
  version "5.4.1"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-5.4.1.tgz#fdc58d9d17f4a4e98d102ded826a9b9759125102"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  dependencies:
    sprintf-js "~1.0.2"

asn1.js@^4.0.0:
  version "4.10.1"
  resolved "https://registry.yarnpkg.com/asn1.js/-/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

assert@~1.1.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/assert/-/assert-1.1.2.tgz#adaa04c46bb58c6dd1f294da3eb26e6228eb6e44"
  dependencies:
    util "0.10.3"

assert@~1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/assert/-/assert-1.3.0.tgz#03939a622582a812cc202320a0b9a56c9b815849"
  dependencies:
    util "0.10.3"

astw@^2.0.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/astw/-/astw-2.2.0.tgz#7bd41784d32493987aeb239b6b4e1c57a873b917"
  dependencies:
    acorn "^4.0.3"

async@~0.2.6:
  version "0.2.10"
  resolved "https://registry.yarnpkg.com/async/-/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

base64-js@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-0.0.2.tgz#024f0f72afa25b75f9c0ee73cd4f55ec1bed9784"

base64-js@0.0.8, base64-js@~0.0.4:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-0.0.8.tgz#1101e9544f4a76b1bc3b26d452ca96d7a35e7978"

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.1.1, bn.js@^4.4.0:
  version "4.11.8"
  resolved "https://registry.yarnpkg.com/bn.js/-/bn.js-4.11.8.tgz#2cde09eb5ee341f484746bb0309b3253b1b1442f"

bops@0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/bops/-/bops-0.0.6.tgz#082d1d55fa01e60dbdc2ebc2dba37f659554cf3a"
  dependencies:
    base64-js "0.0.2"
    to-utf8 "0.0.1"

bouncy@~3.2.0:
  version "3.2.2"
  resolved "https://registry.yarnpkg.com/bouncy/-/bouncy-3.2.2.tgz#82ab4ad7beae05890eed54b9af3c45394b185dc7"
  dependencies:
    optimist "~0.3.5"
    through "~2.3.4"

brace-expansion@^1.0.0, brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/brorand/-/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"

browser-launcher@~0.3.2:
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/browser-launcher/-/browser-launcher-0.3.5.tgz#d9a3663fa064d8155044991c00e61dbcb6730a16"
  dependencies:
    headless "~0.1.3"
    merge "~1.0.0"
    minimist "0.0.5"
    mkdirp "~0.3.3"
    plist "0.2.1"
    xtend "^4.0.0"

browser-pack@^5.0.0:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/browser-pack/-/browser-pack-5.0.1.tgz#4197719b20c6e0aaa09451c5111e53efb6fbc18d"
  dependencies:
    JSONStream "^1.0.3"
    combine-source-map "~0.6.1"
    defined "^1.0.0"
    through2 "^1.0.0"
    umd "^3.0.0"

browser-pack@~2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/browser-pack/-/browser-pack-2.0.1.tgz#5d1c527f56c582677411c4db2a128648ff6bf150"
  dependencies:
    JSONStream "~0.6.4"
    combine-source-map "~0.3.0"
    through "~2.3.4"

browser-resolve@^1.7.0, browser-resolve@^1.7.1:
  version "1.11.2"
  resolved "https://registry.yarnpkg.com/browser-resolve/-/browser-resolve-1.11.2.tgz#8ff09b0a2c421718a1051c260b32e48f442938ce"
  dependencies:
    resolve "1.1.7"

browser-resolve@~1.2.1, browser-resolve@~1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/browser-resolve/-/browser-resolve-1.2.4.tgz#59ae7820a82955ecd32f5fb7c468ac21c4723806"
  dependencies:
    resolve "0.6.3"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/browserify-aes/-/browserify-aes-1.1.1.tgz#38b7ab55edb806ff2dcda1a7f1620773a477c49f"
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/browserify-cipher/-/browserify-cipher-1.0.0.tgz#9988244874bf5ed4e28da95666dcd66ac8fc363a"
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/browserify-des/-/browserify-des-1.0.0.tgz#daa277717470922ed2fe18594118a175439721dd"
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"

browserify-rsa@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/browserify-rsa/-/browserify-rsa-4.0.1.tgz#21e0abfaf6f2029cf2fafb133567a701d4135524"
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.0.4"
  resolved "https://registry.yarnpkg.com/browserify-sign/-/browserify-sign-4.0.4.tgz#aa4eb68e5d7b658baa6bf6a57e630cbd7a93d298"
  dependencies:
    bn.js "^4.1.1"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.2"
    elliptic "^6.0.0"
    inherits "^2.0.1"
    parse-asn1 "^5.0.0"

browserify-zlib@~0.1.2:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/browserify-zlib/-/browserify-zlib-0.1.4.tgz#bb35f8a519f600e0fa6b8485241c979d0141fb2d"
  dependencies:
    pako "~0.2.0"

browserify@11.2.0:
  version "11.2.0"
  resolved "https://registry.yarnpkg.com/browserify/-/browserify-11.2.0.tgz#a11bb9dd209d79572b813f7eeeaf828a5f5c0e4e"
  dependencies:
    JSONStream "^1.0.3"
    assert "~1.3.0"
    browser-pack "^5.0.0"
    browser-resolve "^1.7.1"
    browserify-zlib "~0.1.2"
    buffer "^3.0.0"
    builtins "~0.0.3"
    commondir "0.0.1"
    concat-stream "~1.4.1"
    console-browserify "^1.1.0"
    constants-browserify "~0.0.1"
    crypto-browserify "^3.0.0"
    defined "^1.0.0"
    deps-sort "^1.3.7"
    domain-browser "~1.1.0"
    duplexer2 "~0.0.2"
    events "~1.0.0"
    glob "^4.0.5"
    has "^1.0.0"
    htmlescape "^1.1.0"
    https-browserify "~0.0.0"
    inherits "~2.0.1"
    insert-module-globals "^6.4.1"
    isarray "0.0.1"
    labeled-stream-splicer "^1.0.0"
    module-deps "^3.7.11"
    os-browserify "~0.1.1"
    parents "^1.0.1"
    path-browserify "~0.0.0"
    process "~0.11.0"
    punycode "^1.3.2"
    querystring-es3 "~0.2.0"
    read-only-stream "^1.1.1"
    readable-stream "^2.0.2"
    resolve "^1.1.4"
    shasum "^1.0.0"
    shell-quote "~0.0.1"
    stream-browserify "^2.0.0"
    stream-http "^1.2.0"
    string_decoder "~0.10.0"
    subarg "^1.0.0"
    syntax-error "^1.1.1"
    through2 "^1.0.0"
    timers-browserify "^1.0.1"
    tty-browserify "~0.0.0"
    url "~0.10.1"
    util "~0.10.1"
    vm-browserify "~0.0.1"
    xtend "^4.0.0"

browserify@3.x.x:
  version "3.46.1"
  resolved "https://registry.yarnpkg.com/browserify/-/browserify-3.46.1.tgz#2c2e4a7f2f408178e78c223b5b57b37c2185ad8e"
  dependencies:
    JSONStream "~0.7.1"
    assert "~1.1.0"
    browser-pack "~2.0.0"
    browser-resolve "~1.2.1"
    browserify-zlib "~0.1.2"
    buffer "~2.1.4"
    builtins "~0.0.3"
    commondir "0.0.1"
    concat-stream "~1.4.1"
    console-browserify "~1.0.1"
    constants-browserify "~0.0.1"
    crypto-browserify "~1.0.9"
    deep-equal "~0.1.0"
    defined "~0.0.0"
    deps-sort "~0.1.1"
    derequire "~0.8.0"
    domain-browser "~1.1.0"
    duplexer "~0.1.1"
    events "~1.0.0"
    glob "~3.2.8"
    http-browserify "~1.3.1"
    https-browserify "~0.0.0"
    inherits "~2.0.1"
    insert-module-globals "~6.0.0"
    module-deps "~2.0.0"
    os-browserify "~0.1.1"
    parents "~0.0.1"
    path-browserify "~0.0.0"
    process "^0.7.0"
    punycode "~1.2.3"
    querystring-es3 "0.2.0"
    resolve "~0.6.1"
    shallow-copy "0.0.1"
    shell-quote "~0.0.1"
    stream-browserify "~0.1.0"
    stream-combiner "~0.0.2"
    string_decoder "~0.0.0"
    subarg "0.0.1"
    syntax-error "~1.1.0"
    through2 "~0.4.1"
    timers-browserify "~1.0.1"
    tty-browserify "~0.0.0"
    umd "~2.0.0"
    url "~0.10.1"
    util "~0.10.1"
    vm-browserify "~0.0.1"
    xtend "^3.0.0"

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/buffer-xor/-/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"

buffer@^3.0.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-3.6.0.tgz#a72c936f77b96bf52f5f7e7b467180628551defb"
  dependencies:
    base64-js "0.0.8"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

buffer@~2.1.4:
  version "2.1.13"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-2.1.13.tgz#c88838ebf79f30b8b4a707788470bea8a62c2355"
  dependencies:
    base64-js "~0.0.4"
    ieee754 "~1.1.1"

builtin-status-codes@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/builtin-status-codes/-/builtin-status-codes-1.0.0.tgz#30637ee262978ac07174e16d7f82f0ad06e085ad"

builtins@~0.0.3:
  version "0.0.7"
  resolved "https://registry.yarnpkg.com/builtins/-/builtins-0.0.7.tgz#355219cd6cf18dbe7c01cc7fd2dce765cfdc549a"

callsite@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/callsite/-/callsite-1.0.0.tgz#280398e5d664bd74038b6f0905153e6e8af1bc20"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"

chalk@^1.1.1:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/cipher-base/-/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

combine-source-map@~0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/combine-source-map/-/combine-source-map-0.3.0.tgz#d9e74f593d9cd43807312cb5d846d451efaa9eb7"
  dependencies:
    convert-source-map "~0.3.0"
    inline-source-map "~0.3.0"
    source-map "~0.1.31"

combine-source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/combine-source-map/-/combine-source-map-0.6.1.tgz#9b4a09c316033d768e0f11e029fa2730e079ad96"
  dependencies:
    convert-source-map "~1.1.0"
    inline-source-map "~0.5.0"
    lodash.memoize "~3.0.3"
    source-map "~0.4.2"

commondir@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/commondir/-/commondir-0.0.1.tgz#89f00fdcd51b519c578733fec563e6a6da7f5be2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@~0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-0.1.1.tgz#d7f4e278b90cfc4f0f3ef77fe4c03b40eb3f7900"

concat-stream@~1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.0.1.tgz#018b18bc1c7d073a2dc82aa48442341a2c4dd79f"
  dependencies:
    bops "0.0.6"

concat-stream@~1.4.1, concat-stream@~1.4.5:
  version "1.4.10"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.4.10.tgz#acc3bbf5602cb8cc980c6ac840fa7d8603e3ef36"
  dependencies:
    inherits "~2.0.1"
    readable-stream "~1.1.9"
    typedarray "~0.0.5"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-browserify/-/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  dependencies:
    date-now "^0.1.4"

console-browserify@~1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/console-browserify/-/console-browserify-1.0.3.tgz#d3898d2c3a93102f364197f8874b4f92b5286a8e"

constants-browserify@~0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/constants-browserify/-/constants-browserify-0.0.1.tgz#92577db527ba6c4cf0a4568d84bc031f441e21f2"

convert-source-map@~0.3.0:
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-0.3.5.tgz#f1d802950af7dd2631a1febe0596550c86ab3190"

convert-source-map@~1.1.0:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.1.3.tgz#4829c877e9fe49b3161f3bf3673888e204699860"

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

create-ecdh@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/create-ecdh/-/create-ecdh-4.0.0.tgz#888c723596cdf7612f6498233eebd7a35301737d"
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.2:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/create-hash/-/create-hash-1.1.3.tgz#606042ac8b9262750f483caddab0f5819172d8fd"
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.2, create-hmac@^1.1.4:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/create-hmac/-/create-hmac-1.1.6.tgz#acb9e221a4e17bdb076e90657c42b93e3726cf06"
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

crypto-browserify@^3.0.0:
  version "3.12.0"
  resolved "https://registry.yarnpkg.com/crypto-browserify/-/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

crypto-browserify@~1.0.9:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/crypto-browserify/-/crypto-browserify-1.0.9.tgz#cc5449685dfb85eb11c9828acc7cb87ab5bbfcc0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/date-now/-/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

deep-equal@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/deep-equal/-/deep-equal-0.0.0.tgz#99679d3bbd047156fcd450d3d01eeb9068691e83"

deep-equal@~0.1.0:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/deep-equal/-/deep-equal-0.1.2.tgz#b246c2b80a570a47c11be1d9bd1070ec878b87ce"

deep-equal@~1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/deep-equal/-/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"

define-properties@^1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/define-properties/-/define-properties-1.1.2.tgz#83a73f2fea569898fb737193c8f873caf6d45c94"
  dependencies:
    foreach "^2.0.5"
    object-keys "^1.0.8"

defined@^1.0.0, defined@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/defined/-/defined-1.0.0.tgz#c98d9bcef75674188e110969151199e39b1fa693"

defined@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/defined/-/defined-0.0.0.tgz#f35eea7d705e933baf13b2f03b3f83d921403b3e"

deps-sort@^1.3.7:
  version "1.3.9"
  resolved "https://registry.yarnpkg.com/deps-sort/-/deps-sort-1.3.9.tgz#29dfff53e17b36aecae7530adbbbf622c2ed1a71"
  dependencies:
    JSONStream "^1.0.3"
    shasum "^1.0.0"
    subarg "^1.0.0"
    through2 "^1.0.0"

deps-sort@~0.1.1:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/deps-sort/-/deps-sort-0.1.2.tgz#daa2fb614a17c9637d801e2f55339ae370f3611a"
  dependencies:
    JSONStream "~0.6.4"
    minimist "~0.0.1"
    through "~2.3.4"

derequire@~0.8.0:
  version "0.8.0"
  resolved "https://registry.yarnpkg.com/derequire/-/derequire-0.8.0.tgz#c1f7f1da2cede44adede047378f03f444e9c4c0d"
  dependencies:
    esprima-fb "^3001.1.0-dev-harmony-fb"
    esrefactor "~0.1.0"
    estraverse "~1.5.0"

des.js@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/des.js/-/des.js-1.0.0.tgz#c074d2e2aa6a8a9a07dbd61f9a15c2cd83ec8ecc"
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

detective@^4.0.0:
  version "4.7.1"
  resolved "https://registry.yarnpkg.com/detective/-/detective-4.7.1.tgz#0eca7314338442febb6d65da54c10bb1c82b246e"
  dependencies:
    acorn "^5.2.1"
    defined "^1.0.0"

detective@~3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/detective/-/detective-3.1.0.tgz#77782444ab752b88ca1be2e9d0a0395f1da25eed"
  dependencies:
    escodegen "~1.1.0"
    esprima-fb "3001.1.0-dev-harmony-fb"

diff@^2.2.1:
  version "2.2.3"
  resolved "https://registry.yarnpkg.com/diff/-/diff-2.2.3.tgz#60eafd0d28ee906e4e8ff0a52c1229521033bf99"

diffie-hellman@^5.0.0:
  version "5.0.2"
  resolved "https://registry.yarnpkg.com/diffie-hellman/-/diffie-hellman-5.0.2.tgz#b5835739270cfe26acf632099fded2a07f209e5e"
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

domain-browser@~1.1.0:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/domain-browser/-/domain-browser-1.1.7.tgz#867aa4b093faa05f1de08c06f4d7b21fdf8698bc"

duplexer2@0.0.2, duplexer2@~0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/duplexer2/-/duplexer2-0.0.2.tgz#c614dcf67e2fb14995a91711e5a617e8a60a31db"
  dependencies:
    readable-stream "~1.1.9"

duplexer@^0.1.1, duplexer@~0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/duplexer/-/duplexer-0.1.1.tgz#ace6ff808c1ce66b57d1ebf97977acb02334cfc1"

ecstatic@~0.4.5:
  version "0.4.13"
  resolved "https://registry.yarnpkg.com/ecstatic/-/ecstatic-0.4.13.tgz#9cb6eaffe211b9c84efb3f553cde2c3002717b29"
  dependencies:
    ent "0.0.x"
    mime "1.2.x"
    optimist "~0.3.5"

elliptic@^6.0.0:
  version "6.4.0"
  resolved "https://registry.yarnpkg.com/elliptic/-/elliptic-6.4.0.tgz#cac9af8762c85836187003c8dfe193e5e2eae5df"
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

ent@0.0.x, ent@~0.0.5:
  version "0.0.7"
  resolved "https://registry.yarnpkg.com/ent/-/ent-0.0.7.tgz#835d4e7f9e7a8d4921c692e9010ec976da5e9949"

es-abstract@^1.5.0:
  version "1.10.0"
  resolved "https://registry.yarnpkg.com/es-abstract/-/es-abstract-1.10.0.tgz#1ecb36c197842a00d8ee4c2dfd8646bb97d60864"
  dependencies:
    es-to-primitive "^1.1.1"
    function-bind "^1.1.1"
    has "^1.0.1"
    is-callable "^1.1.3"
    is-regex "^1.0.4"

es-to-primitive@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/es-to-primitive/-/es-to-primitive-1.1.1.tgz#45355248a88979034b6792e19bb81f2b7975dd0d"
  dependencies:
    is-callable "^1.1.1"
    is-date-object "^1.0.1"
    is-symbol "^1.0.1"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

escodegen@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/escodegen/-/escodegen-1.1.0.tgz#c663923f6e20aad48d0c0fa49f31c6d4f49360cf"
  dependencies:
    esprima "~1.0.4"
    estraverse "~1.5.0"
    esutils "~1.0.0"
  optionalDependencies:
    source-map "~0.1.30"

escope@~0.0.13:
  version "0.0.16"
  resolved "https://registry.yarnpkg.com/escope/-/escope-0.0.16.tgz#418c7a0afca721dafe659193fd986283e746538f"
  dependencies:
    estraverse ">= 0.0.2"

esprima-fb@3001.1.0-dev-harmony-fb, esprima-fb@^3001.1.0-dev-harmony-fb:
  version "3001.1.0-dev-harmony-fb"
  resolved "https://registry.yarnpkg.com/esprima-fb/-/esprima-fb-3001.0001.0000-dev-harmony-fb.tgz#b77d37abcd38ea0b77426bb8bc2922ce6b426411"

esprima@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.0.tgz#4499eddcd1110e0b218bacf2fa7f7f59f55ca804"

esprima@~1.0.2, esprima@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-1.0.4.tgz#9f557e08fc3b4d26ece9dd34f8fbf476b62585ad"

esrefactor@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/esrefactor/-/esrefactor-0.1.0.tgz#d142795a282339ab81e936b5b7a21b11bf197b13"
  dependencies:
    escope "~0.0.13"
    esprima "~1.0.2"
    estraverse "~0.0.4"

"estraverse@>= 0.0.2":
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"

estraverse@~0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-0.0.4.tgz#01a0932dfee574684a598af5a67c3bf9b6428db2"

estraverse@~1.5.0:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-1.5.1.tgz#867a3e8e58a9f84618afb6c2ddbcd916b7cbaf71"

esutils@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-1.0.0.tgz#8151d358e20c8acc7fb745e7472c0025fe496570"

events@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/events/-/events-1.0.2.tgz#75849dcfe93d10fb057c30055afdbd51d06a8e24"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

figures@^1.4.0:
  version "1.7.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

foreach@^2.0.5:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/foreach/-/foreach-2.0.5.tgz#0bee005018aeb260d0a3af3ae658dd0136ec1b99"

function-bind@^1.0.2, function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"

function-bind@~1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.0.2.tgz#c2873b69c5e6d7cefae47d2555172926c8c2e05e"

glob@^4.0.5:
  version "4.5.3"
  resolved "https://registry.yarnpkg.com/glob/-/glob-4.5.3.tgz#c6cb73d3226c1efef04de3c56d012f03377ee15f"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "^2.0.1"
    once "^1.3.0"

glob@~3.2.1, glob@~3.2.8:
  version "3.2.11"
  resolved "https://registry.yarnpkg.com/glob/-/glob-3.2.11.tgz#4a973f635b9190f715d10987d5c00fd2815ebe3d"
  dependencies:
    inherits "2"
    minimatch "0.3"

glob@~5.0.3:
  version "5.0.15"
  resolved "https://registry.yarnpkg.com/glob/-/glob-5.0.15.tgz#1bc936b9e02f4a603fcc222ecf7633d30b8b93b1"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has@^1.0.0, has@^1.0.1, has@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/has/-/has-1.0.1.tgz#8461733f538b0837c9361e39a9ab9e9704dc2f28"
  dependencies:
    function-bind "^1.0.2"

hash-base@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/hash-base/-/hash-base-2.0.2.tgz#66ea1d856db4e8a5470cadf6fce23ae5244ef2e1"
  dependencies:
    inherits "^2.0.1"

hash-base@^3.0.0:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/hash-base/-/hash-base-3.0.4.tgz#5fc8686847ecd73499403319a6b0a3f3f6ae4918"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/hash.js/-/hash.js-1.1.3.tgz#340dedbe6290187151c1ea1d777a3448935df846"
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.0"

headless@~0.1.3:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/headless/-/headless-0.1.7.tgz#6e62fae668947f88184d5c156ede7c5695a7e9c8"

hirestime@^1.0.6:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/hirestime/-/hirestime-1.0.7.tgz#2d5271ea84356cec3f25da8c56a9402f8fc0a700"

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

htmlescape@^1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/htmlescape/-/htmlescape-1.1.1.tgz#3a03edc2214bca3b66424a3e7959349509cb0351"

http-browserify@~1.3.1:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/http-browserify/-/http-browserify-1.3.2.tgz#b562c34479349a690d7a6597df495aefa8c604f5"
  dependencies:
    Base64 "~0.2.0"
    inherits "~2.0.1"

https-browserify@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/https-browserify/-/https-browserify-0.0.1.tgz#3f91365cabe60b77ed0ebba24b454e3e09d95a82"

ieee754@^1.1.4, ieee754@~1.1.1:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.8.tgz#be33d40ac10ef1926701f6f08a2d86fbfd1ad3e4"

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/indexof/-/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inherits@2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"

inline-source-map@~0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/inline-source-map/-/inline-source-map-0.3.1.tgz#a528b514e689fce90db3089e870d92f527acb5eb"
  dependencies:
    source-map "~0.3.0"

inline-source-map@~0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/inline-source-map/-/inline-source-map-0.5.0.tgz#4a4c5dd8e4fb5e9b3cda60c822dfadcaee66e0af"
  dependencies:
    source-map "~0.4.0"

insert-module-globals@^6.4.1:
  version "6.6.3"
  resolved "https://registry.yarnpkg.com/insert-module-globals/-/insert-module-globals-6.6.3.tgz#20638e29a30f9ed1ca2e3a825fbc2cba5246ddfc"
  dependencies:
    JSONStream "^1.0.3"
    combine-source-map "~0.6.1"
    concat-stream "~1.4.1"
    is-buffer "^1.1.0"
    lexical-scope "^1.2.0"
    process "~0.11.0"
    through2 "^1.0.0"
    xtend "^4.0.0"

insert-module-globals@~6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/insert-module-globals/-/insert-module-globals-6.0.0.tgz#ee8aeb9dee16819e33aa14588a558824af0c15dc"
  dependencies:
    JSONStream "~0.7.1"
    concat-stream "~1.4.1"
    lexical-scope "~1.1.0"
    process "~0.6.0"
    through "~2.3.4"
    xtend "^3.0.0"

is-buffer@^1.1.0:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"

is-callable@^1.1.1, is-callable@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.1.3.tgz#86eb75392805ddc33af71c92a0eedf74ee7604b2"

is-date-object@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-date-object/-/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"

is-finite@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-finite/-/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-regex@^1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/is-regex/-/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
  dependencies:
    has "^1.0.1"

is-symbol@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-symbol/-/is-symbol-1.0.1.tgz#3cc59f00025194b6ab2e38dbae6689256b660572"

isarray@0.0.1, isarray@~0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

js-yaml@^3.4.6:
  version "3.10.0"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.10.0.tgz#2e78441646bd4682e963f22b6e92823c309c62dc"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

json-stable-stringify@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/json-stable-stringify/-/json-stable-stringify-0.0.1.tgz#611c23e814db375527df851193db59dd2af27f45"
  dependencies:
    jsonify "~0.0.0"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"

jsonparse@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/jsonparse/-/jsonparse-0.0.5.tgz#330542ad3f0a654665b778f3eb2d9a9fa507ac64"

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/jsonparse/-/jsonparse-1.3.1.tgz#3f4dae4a91fac315f71062f8521cc239f1366280"

labeled-stream-splicer@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/labeled-stream-splicer/-/labeled-stream-splicer-1.0.2.tgz#4615331537784981e8fd264e1f3a434c4e0ddd65"
  dependencies:
    inherits "^2.0.1"
    isarray "~0.0.1"
    stream-splicer "^1.1.0"

lexical-scope@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/lexical-scope/-/lexical-scope-1.2.0.tgz#fcea5edc704a4b3a8796cdca419c3a0afaf22df4"
  dependencies:
    astw "^2.0.0"

lexical-scope@~1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/lexical-scope/-/lexical-scope-1.1.1.tgz#debac1067435f1359d90fcfd9e94bcb2ee47b2bf"
  dependencies:
    astw "^2.0.0"

lodash.memoize@~3.0.3:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/lodash.memoize/-/lodash.memoize-3.0.4.tgz#2dcbd2c287cbc0a55cc42328bd0c736150d53e3f"

lru-cache@2:
  version "2.7.3"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-2.7.3.tgz#6d4524e8b955f95d4f5b58851ce21dd72fb4e952"

md5.js@^1.3.4:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/md5.js/-/md5.js-1.3.4.tgz#e9bdbde94a20a5ac18b04340fc5764d5b09d901d"
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

merge@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/merge/-/merge-1.0.0.tgz#b443ab46d837c491e6222056ab0f7933ecb3568f"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/miller-rabin/-/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime@1.2.x:
  version "1.2.11"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.2.11.tgz#58203eed86e3a5ef17aed2b7d9ebd47f0a60dd10"

minimalistic-assert@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/minimalistic-assert/-/minimalistic-assert-1.0.0.tgz#702be2dda6b37f4836bcb3f5db56641b64a1d3d3"

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"

minimatch@0.3:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-0.3.0.tgz#275d8edaac4f1bb3326472089e7949c8394699dd"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

"minimatch@2 || 3":
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^2.0.1:
  version "2.0.10"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-2.0.10.tgz#8d087c39c6b38c001b97fca7ce6d0e1e80afbac7"
  dependencies:
    brace-expansion "^1.0.0"

minimist@0.0.5:
  version "0.0.5"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.5.tgz#d7aa327bcecf518f9106ac6b8f003fa3bcea8566"

minimist@^1.1.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

minimist@~0.0.1, minimist@~0.0.7, minimist@~0.0.9:
  version "0.0.10"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.10.tgz#de3f98543dbf96082be48ad1a0c7cda836301dcf"

mkdirp@~0.3.3:
  version "0.3.5"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.3.5.tgz#de3e5f8961c88c787ee1368df849ac4413eca8d7"

module-deps@^3.7.11:
  version "3.9.1"
  resolved "https://registry.yarnpkg.com/module-deps/-/module-deps-3.9.1.tgz#ea75caf9199090d25b0d5512b5acacb96e7f87f3"
  dependencies:
    JSONStream "^1.0.3"
    browser-resolve "^1.7.0"
    concat-stream "~1.4.5"
    defined "^1.0.0"
    detective "^4.0.0"
    duplexer2 "0.0.2"
    inherits "^2.0.1"
    parents "^1.0.0"
    readable-stream "^1.1.13"
    resolve "^1.1.3"
    stream-combiner2 "~1.0.0"
    subarg "^1.0.0"
    through2 "^1.0.0"
    xtend "^4.0.0"

module-deps@~2.0.0:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/module-deps/-/module-deps-2.0.6.tgz#b999321c73ac33580f00712c0f3075fdca42563f"
  dependencies:
    JSONStream "~0.7.1"
    browser-resolve "~1.2.4"
    concat-stream "~1.4.5"
    detective "~3.1.0"
    duplexer2 "0.0.2"
    inherits "~2.0.1"
    minimist "~0.0.9"
    parents "0.0.2"
    readable-stream "^1.0.27-1"
    resolve "~0.6.3"
    stream-combiner "~0.1.0"
    through2 "~0.4.1"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

object-assign@^4.1.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-inspect@~0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/object-inspect/-/object-inspect-0.1.3.tgz#d05a65c2e34fe8225d9fda2e484e4e47b7e2f490"
  dependencies:
    tape "~1.0.4"

object-inspect@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/object-inspect/-/object-inspect-1.0.2.tgz#a97885b553e575eb4009ebc09bdda9b1cd21979a"

object-keys@^1.0.4, object-keys@^1.0.8:
  version "1.0.11"
  resolved "https://registry.yarnpkg.com/object-keys/-/object-keys-1.0.11.tgz#c54601778ad560f1142ce0e01bcca8b56d13426d"

object-keys@~0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/object-keys/-/object-keys-0.4.0.tgz#28a6aae7428dd2c3a92f3d95f21335dd204e0336"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

optimist@~0.3.5:
  version "0.3.7"
  resolved "https://registry.yarnpkg.com/optimist/-/optimist-0.3.7.tgz#c90941ad59e4273328923074d2cf2e7cbc6ec0d9"
  dependencies:
    wordwrap "~0.0.2"

optimist@~0.5.2:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/optimist/-/optimist-0.5.2.tgz#85c8c1454b3315e4a78947e857b1df033450bfbc"
  dependencies:
    wordwrap "~0.0.2"

ordered-emitter@~0.1.0:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/ordered-emitter/-/ordered-emitter-0.1.1.tgz#aa20bdafbdcc1631834a350f68b4ef8eb34eed7b"

os-browserify@~0.1.1:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/os-browserify/-/os-browserify-0.1.2.tgz#49ca0293e0b19590a5f5de10c7f265a617d8fe54"

pako@~0.2.0:
  version "0.2.9"
  resolved "https://registry.yarnpkg.com/pako/-/pako-0.2.9.tgz#f3f7522f4ef782348da8161bad9ecfd51bf83a75"

parents@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/parents/-/parents-0.0.2.tgz#67147826e497d40759aaf5ba4c99659b6034d302"

parents@^1.0.0, parents@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/parents/-/parents-1.0.1.tgz#fedd4d2bf193a77745fe71e371d73c3307d9c751"
  dependencies:
    path-platform "~0.11.15"

parents@~0.0.1:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/parents/-/parents-0.0.3.tgz#fa212f024d9fa6318dbb6b4ce676c8be493b9c43"
  dependencies:
    path-platform "^0.0.1"

parse-asn1@^5.0.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/parse-asn1/-/parse-asn1-5.1.0.tgz#37c4f9b7ed3ab65c74817b5f2480937fbf97c712"
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"

parse-ms@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/parse-ms/-/parse-ms-1.0.1.tgz#56346d4749d78f23430ca0c713850aef91aa361d"

path-browserify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/path-browserify/-/path-browserify-0.0.0.tgz#a0b870729aae214005b7d5032ec2cbbb0fb4451a"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-parse@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.5.tgz#3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1"

path-platform@^0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/path-platform/-/path-platform-0.0.1.tgz#b5585d7c3c463d89aa0060d86611cf1afd617e2a"

path-platform@~0.11.15:
  version "0.11.15"
  resolved "https://registry.yarnpkg.com/path-platform/-/path-platform-0.11.15.tgz#e864217f74c36850f0852b78dc7bf7d4a5721bf2"

pbkdf2@^3.0.3:
  version "3.0.14"
  resolved "https://registry.yarnpkg.com/pbkdf2/-/pbkdf2-3.0.14.tgz#a35e13c64799b06ce15320f459c230e68e73bade"
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

plist@0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/plist/-/plist-0.2.1.tgz#f3a3de07885d773e66d8a96782f1bec28cf2b2d0"
  dependencies:
    sax "0.1.x"

plur@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/plur/-/plur-1.0.0.tgz#db85c6814f5e5e5a3b49efc28d604fec62975156"

pretty-ms@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/pretty-ms/-/pretty-ms-2.1.0.tgz#4257c256df3fb0b451d6affaab021884126981dc"
  dependencies:
    is-finite "^1.0.1"
    parse-ms "^1.0.0"
    plur "^1.0.0"

process-nextick-args@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-2.0.0.tgz#a37d732f4271b4ab1ad070d35508e8290788ffaa"

process@^0.7.0:
  version "0.7.0"
  resolved "https://registry.yarnpkg.com/process/-/process-0.7.0.tgz#c52208161a34adf3812344ae85d3e6150469389d"

process@~0.11.0:
  version "0.11.10"
  resolved "https://registry.yarnpkg.com/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"

process@~0.5.1:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/process/-/process-0.5.2.tgz#1638d8a8e34c2f440a91db95ab9aeb677fc185cf"

process@~0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/process/-/process-0.6.0.tgz#7dd9be80ffaaedd4cb628f1827f1cbab6dc0918f"

public-encrypt@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/public-encrypt/-/public-encrypt-4.0.0.tgz#39f699f3a46560dd5ebacbca693caf7c65c18cc6"
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"

punycode@^1.3.2:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

punycode@~1.2.3:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-1.2.4.tgz#54008ac972aec74175def9cba6df7fa9d3918740"

querystring-es3@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring-es3/-/querystring-es3-0.2.0.tgz#c365a08a69c443accfeb3a9deab35e3f0abaa476"

querystring-es3@~0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"

ramda@^0.18.0:
  version "0.18.0"
  resolved "https://registry.yarnpkg.com/ramda/-/ramda-0.18.0.tgz#c6e3c5d4b9ab1f7906727fdeeb039152a85d4db3"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/randombytes/-/randombytes-2.0.6.tgz#d302c522948588848a8d300c932b44c24231da80"
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/randomfill/-/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

read-only-stream@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/read-only-stream/-/read-only-stream-1.1.1.tgz#5da77c799ed1388d3ef88a18471bb5924f8a0ba1"
  dependencies:
    readable-stream "^1.0.31"
    readable-wrap "^1.0.0"

"readable-stream@>=1.1.13-1 <1.2.0-0", readable-stream@^1.0.27-1, readable-stream@^1.0.31, readable-stream@^1.1.13, readable-stream@^1.1.13-1, readable-stream@~1.1.9:
  version "1.1.14"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-1.1.14.tgz#7cf4c54ef648e3813084c636dd2079e166c081d9"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@^2.0.2, readable-stream@^2.0.4:
  version "2.3.4"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.4.tgz#c946c3f47fa7d8eabc0b6150f4a12f69a4574071"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.0.3"
    util-deprecate "~1.0.1"

readable-stream@~1.0.17:
  version "1.0.34"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-wrap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/readable-wrap/-/readable-wrap-1.0.0.tgz#3b5a211c631e12303a54991c806c17e7ae206bff"
  dependencies:
    readable-stream "^1.1.13-1"

resolve@0.6.3, resolve@~0.6.1, resolve@~0.6.3:
  version "0.6.3"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-0.6.3.tgz#dd957982e7e736debdf53b58a4dd91754575dd46"

resolve@1.1.7:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"

resolve@^1.1.3, resolve@^1.1.4:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.5.0.tgz#1f09acce796c9a762579f31b2c1cc4c3cddf9f36"
  dependencies:
    path-parse "^1.0.5"

resolve@~0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-0.3.1.tgz#34c63447c664c70598d1c9b126fc43b2a24310a4"

resolve@~0.4.0:
  version "0.4.3"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-0.4.3.tgz#dcadad202e7cacc2467e3a38800211f42f9c13df"

resumer@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/resumer/-/resumer-0.0.0.tgz#f1e8f461e4064ba39e82af3cdc2a8c893d076759"
  dependencies:
    through "~2.3.4"

rfile@~1.0, rfile@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/rfile/-/rfile-1.0.0.tgz#59708cf90ca1e74c54c3cfc5c36fdb9810435261"
  dependencies:
    callsite "~1.0.0"
    resolve "~0.3.0"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/ripemd160/-/ripemd160-2.0.1.tgz#0f4584295c53a3628af7e6d79aca21ce57d1c6e7"
  dependencies:
    hash-base "^2.0.0"
    inherits "^2.0.1"

ruglify@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/ruglify/-/ruglify-1.0.0.tgz#dc8930e2a9544a274301cc9972574c0d0986b675"
  dependencies:
    rfile "~1.0"
    uglify-js "~2.2"

rx-node@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/rx-node/-/rx-node-1.0.2.tgz#151240725a79e857360ab06cc626799965e094de"
  dependencies:
    rx "*"

rx@*, rx@^4.0.7:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/rx/-/rx-4.1.0.tgz#a5f13ff79ef3b740fe30aa803fb09f98805d4782"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.1.tgz#893312af69b2123def71f57889001671eeb2c853"

sax@0.1.x:
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/sax/-/sax-0.1.5.tgz#d1829a6120fa01665eb4dbff6c43f29fd6d61471"

sha.js@^2.4.0, sha.js@^2.4.8, sha.js@~2.4.4:
  version "2.4.10"
  resolved "https://registry.yarnpkg.com/sha.js/-/sha.js-2.4.10.tgz#b1fde5cd7d11a5626638a07c604ab909cfa31f9b"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-copy@0.0.1, shallow-copy@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/shallow-copy/-/shallow-copy-0.0.1.tgz#415f42702d73d810330292cc5ee86eae1a11a170"

shasum@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/shasum/-/shasum-1.0.2.tgz#e7012310d8f417f4deb5712150e5678b87ae565f"
  dependencies:
    json-stable-stringify "~0.0.0"
    sha.js "~2.4.4"

shell-quote@~0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/shell-quote/-/shell-quote-0.0.1.tgz#1a41196f3c0333c482323593d6886ecf153dd986"

shell-quote@~1.3.1:
  version "1.3.3"
  resolved "https://registry.yarnpkg.com/shell-quote/-/shell-quote-1.3.3.tgz#07b8826f427c052511e8b5627639e172596e8e4b"

sigmund@~1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/sigmund/-/sigmund-1.0.1.tgz#3ff21f198cad2175f9f3b781853fd94d0d19b590"

source-map@0.1.34:
  version "0.1.34"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.1.34.tgz#a7cfe89aec7b1682c3b198d0acfb47d7d090566b"
  dependencies:
    amdefine ">=0.0.4"

source-map@~0.1.30, source-map@~0.1.31, source-map@~0.1.7:
  version "0.1.43"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.1.43.tgz#c24bc146ca517c1471f5dacbe2571b2b7f9e3346"
  dependencies:
    amdefine ">=0.0.4"

source-map@~0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.3.0.tgz#8586fb9a5a005e5b501e21cd18b6f21b457ad1f9"
  dependencies:
    amdefine ">=0.0.4"

source-map@~0.4.0, source-map@~0.4.2:
  version "0.4.4"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@~0.5.1:
  version "0.5.7"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"

split@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/split/-/split-1.0.1.tgz#605bd9be303aa59fb35f9229fbea0ddec9ea07d9"
  dependencies:
    through "2"

split@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/split/-/split-0.1.2.tgz#f0710744c453d551fc7143ead983da6014e336cc"
  dependencies:
    through "1"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

stream-browserify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-2.0.1.tgz#66266ee5f9bdb9940a4e4514cafb43bb71e5c9db"
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-browserify@~0.1.0:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/stream-browserify/-/stream-browserify-0.1.3.tgz#95cf1b369772e27adaf46352265152689c6c4be9"
  dependencies:
    inherits "~2.0.1"
    process "~0.5.1"

stream-combiner2@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/stream-combiner2/-/stream-combiner2-1.0.2.tgz#ba72a6b50cbfabfa950fc8bc87604bd01eb60671"
  dependencies:
    duplexer2 "~0.0.2"
    through2 "~0.5.1"

stream-combiner@~0.0.2:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/stream-combiner/-/stream-combiner-0.0.4.tgz#4d5e433c185261dde623ca3f44c586bcf5c4ad14"
  dependencies:
    duplexer "~0.1.1"

stream-combiner@~0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/stream-combiner/-/stream-combiner-0.1.0.tgz#0dc389a3c203f8f4d56368f95dde52eb9269b5be"
  dependencies:
    duplexer "~0.1.1"
    through "~2.3.4"

stream-http@^1.2.0:
  version "1.7.1"
  resolved "https://registry.yarnpkg.com/stream-http/-/stream-http-1.7.1.tgz#d3d2a6e14c36a38b9dafb199aee7bbc570519978"
  dependencies:
    builtin-status-codes "^1.0.0"
    foreach "^2.0.5"
    indexof "0.0.1"
    inherits "^2.0.1"
    object-keys "^1.0.4"
    xtend "^4.0.0"

stream-splicer@^1.1.0:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/stream-splicer/-/stream-splicer-1.3.2.tgz#3c0441be15b9bf4e226275e6dc83964745546661"
  dependencies:
    indexof "0.0.1"
    inherits "^2.0.1"
    isarray "~0.0.1"
    readable-stream "^1.1.13-1"
    readable-wrap "^1.0.0"
    through2 "^1.0.0"

string.prototype.trim@^1.1.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/string.prototype.trim/-/string.prototype.trim-1.1.2.tgz#d04de2c89e137f4d7d206f086b5ed2fae6be8cea"
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.5.0"
    function-bind "^1.0.2"

string_decoder@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-0.0.1.tgz#f5472d0a8d1650ec823752d24e6fd627b39bf141"

string_decoder@~0.10.0, string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

string_decoder@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.0.3.tgz#0fc67d7c141825de94282dd536bec6b9bce860ab"
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

subarg@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/subarg/-/subarg-0.0.1.tgz#3d56b07dacfbc45bbb63f7672b43b63e46368e3a"
  dependencies:
    minimist "~0.0.7"

subarg@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/subarg/-/subarg-1.0.0.tgz#f62cf17581e996b48fc965699f54c06ae268b8d2"
  dependencies:
    minimist "^1.1.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

syntax-error@^1.1.1:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/syntax-error/-/syntax-error-1.4.0.tgz#2d9d4ff5c064acb711594a3e3b95054ad51d907c"
  dependencies:
    acorn-node "^1.2.0"

syntax-error@~1.1.0:
  version "1.1.6"
  resolved "https://registry.yarnpkg.com/syntax-error/-/syntax-error-1.1.6.tgz#b4549706d386cc1c1dc7c2423f18579b6cade710"
  dependencies:
    acorn "^2.7.0"

tap-finished@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/tap-finished/-/tap-finished-0.0.1.tgz#08b5b543fdc04830290c6c561279552e71c4bd67"
  dependencies:
    tap-parser "~0.2.0"
    through "~2.3.4"

tap-parser@~0.2.0:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/tap-parser/-/tap-parser-0.2.1.tgz#8e1e823f2114ee21d032e2f31e4fb642a296f50b"
  dependencies:
    split "~0.1.2"

tape@4.2.1:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/tape/-/tape-4.2.1.tgz#1a0ed63cc86bfaa84ebb3bb311f09d8520416216"
  dependencies:
    deep-equal "~1.0.0"
    defined "~1.0.0"
    function-bind "~1.0.2"
    glob "~5.0.3"
    has "~1.0.1"
    inherits "~2.0.1"
    object-inspect "~1.0.0"
    resumer "~0.0.0"
    string.prototype.trim "^1.1.1"
    through "~2.3.4"

tape@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/tape/-/tape-1.0.4.tgz#e2e8e5c6dd3f00fdc2a5e4514f62fc221e59f9c4"
  dependencies:
    deep-equal "~0.0.0"
    defined "~0.0.0"
    jsonify "~0.0.0"
    through "~2.3.4"

testling@1.7.1:
  version "1.7.1"
  resolved "https://registry.yarnpkg.com/testling/-/testling-1.7.1.tgz#bfcfa877c8b15dd28d920692c03d8d64ca47874e"
  dependencies:
    bouncy "~3.2.0"
    browser-launcher "~0.3.2"
    browserify "3.x.x"
    concat-stream "~1.0.0"
    ecstatic "~0.4.5"
    ent "~0.0.5"
    glob "~3.2.1"
    jsonify "~0.0.0"
    object-inspect "~0.1.3"
    optimist "~0.5.2"
    resolve "~0.4.0"
    shallow-copy "~0.0.0"
    shell-quote "~1.3.1"
    tap-finished "~0.0.0"
    win-spawn "~2.0.0"
    xhr-write-stream "~0.1.2"

through2@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/through2/-/through2-1.1.1.tgz#0847cbc4449f3405574dbdccd9bb841b83ac3545"
  dependencies:
    readable-stream ">=1.1.13-1 <1.2.0-0"
    xtend ">=4.0.0 <4.1.0-0"

through2@~0.4.1:
  version "0.4.2"
  resolved "https://registry.yarnpkg.com/through2/-/through2-0.4.2.tgz#dbf5866031151ec8352bb6c4db64a2292a840b9b"
  dependencies:
    readable-stream "~1.0.17"
    xtend "~2.1.1"

through2@~0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/through2/-/through2-0.5.1.tgz#dfdd012eb9c700e2323fd334f38ac622ab372da7"
  dependencies:
    readable-stream "~1.0.17"
    xtend "~3.0.0"

through@1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/through/-/through-1.1.2.tgz#344a5425a3773314ca7e0eb6512fbafaf76c0bfe"

through@2, "through@>=2.2.7 <3", through@~2.3.4:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

through@~2.2.7:
  version "2.2.7"
  resolved "https://registry.yarnpkg.com/through/-/through-2.2.7.tgz#6e8e21200191d4eb6a99f6f010df46aa1c6eb2bd"

timers-browserify@^1.0.1:
  version "1.4.2"
  resolved "https://registry.yarnpkg.com/timers-browserify/-/timers-browserify-1.4.2.tgz#c9c58b575be8407375cb5e2462dacee74359f41d"
  dependencies:
    process "~0.11.0"

timers-browserify@~1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/timers-browserify/-/timers-browserify-1.0.3.tgz#ffba70c9c12eed916fd67318e629ac6f32295551"
  dependencies:
    process "~0.5.1"

to-utf8@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/to-utf8/-/to-utf8-0.0.1.tgz#d17aea72ff2fba39b9e43601be7b3ff72e089852"

tty-browserify@~0.0.0:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/tty-browserify/-/tty-browserify-0.0.1.tgz#3f05251ee17904dfd0677546670db9651682b811"

typedarray@~0.0.5:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

uglify-js@2.5.0:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.5.0.tgz#4ab5d65a4730ecb7a4fb62d3f499e2054d98fba1"
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.5.4"

uglify-js@~2.2:
  version "2.2.5"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.2.5.tgz#a6e02a70d839792b9780488b7b8b184c095c99c7"
  dependencies:
    optimist "~0.3.5"
    source-map "~0.1.7"

uglify-js@~2.4.0:
  version "2.4.24"
  resolved "https://registry.yarnpkg.com/uglify-js/-/uglify-js-2.4.24.tgz#fad5755c1e1577658bb06ff9ab6e548c95bebd6e"
  dependencies:
    async "~0.2.6"
    source-map "0.1.34"
    uglify-to-browserify "~1.0.0"
    yargs "~3.5.4"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

umd@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/umd/-/umd-3.0.1.tgz#8ae556e11011f63c2596708a8837259f01b3d60e"

umd@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/umd/-/umd-2.0.0.tgz#749683b0d514728ae0e1b6195f5774afc0ad4f8f"
  dependencies:
    rfile "~1.0.0"
    ruglify "~1.0.0"
    through "~2.3.4"
    uglify-js "~2.4.0"

url@~0.10.1:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/url/-/url-0.10.3.tgz#021e4d9c7705f21bbf37d03ceb58767402774c64"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

util@0.10.3, util@~0.10.1:
  version "0.10.3"
  resolved "https://registry.yarnpkg.com/util/-/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  dependencies:
    inherits "2.0.1"

vm-browserify@~0.0.1:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/vm-browserify/-/vm-browserify-0.0.4.tgz#5d7ea45bbef9e4a6ff65f95438e0a87c357d5a73"
  dependencies:
    indexof "0.0.1"

win-spawn@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/win-spawn/-/win-spawn-2.0.0.tgz#397a29130ec98d0aa0bc86baa4621393effd0b07"

window-size@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

xhr-write-stream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/xhr-write-stream/-/xhr-write-stream-0.1.2.tgz#e357848e0d039b411fdd5b3bf81be47ee5ce26aa"
  dependencies:
    concat-stream "~0.1.0"
    ordered-emitter "~0.1.0"

"xtend@>=4.0.0 <4.1.0-0", xtend@^4.0.0, xtend@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"

xtend@^3.0.0, xtend@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-3.0.0.tgz#5cce7407baf642cba7becda568111c493f59665a"

xtend@~2.1.1:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-2.1.2.tgz#6efecc2a4dad8e6962c4901b337ce7ba87b5d28b"
  dependencies:
    object-keys "~0.4.0"

yargs@~3.5.4:
  version "3.5.4"
  resolved "https://registry.yarnpkg.com/yargs/-/yargs-3.5.4.tgz#d8aff8f665e94c34bd259bdebd1bfaf0ddd35361"
  dependencies:
    camelcase "^1.0.2"
    decamelize "^1.0.0"
    window-size "0.1.0"
    wordwrap "0.0.2"
