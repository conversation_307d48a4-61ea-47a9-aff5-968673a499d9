'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var react = require('react');
var proxyCompare = require('proxy-compare');
var useSyncExternalStoreExports = require('use-sync-external-store/shim');
var vanilla = require('valtio/vanilla');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var useSyncExternalStoreExports__default = /*#__PURE__*/_interopDefaultLegacy(useSyncExternalStoreExports);

var useSyncExternalStore = useSyncExternalStoreExports__default["default"].useSyncExternalStore;

var useAffectedDebugValue = function useAffectedDebugValue(state, affected) {
  var pathList = react.useRef();
  react.useEffect(function () {
    pathList.current = proxyCompare.affectedToPathList(state, affected);
  });
  react.useDebugValue(pathList.current);
};

function useSnapshot(proxyObject, options) {
  var notifyInSync = options == null ? void 0 : options.sync;
  var lastSnapshot = react.useRef();
  var lastAffected = react.useRef();
  var inRender = true;
  var currSnapshot = useSyncExternalStore(react.useCallback(function (callback) {
    var unsub = vanilla.subscribe(proxyObject, callback, notifyInSync);
    callback();
    return unsub;
  }, [proxyObject, notifyInSync]), function () {
    var nextSnapshot = vanilla.snapshot(proxyObject);

    try {
      if (!inRender && lastSnapshot.current && lastAffected.current && !proxyCompare.isChanged(lastSnapshot.current, nextSnapshot, lastAffected.current, new WeakMap())) {
        return lastSnapshot.current;
      }
    } catch (e) {}

    return nextSnapshot;
  }, function () {
    return vanilla.snapshot(proxyObject);
  });
  inRender = false;
  var currAffected = new WeakMap();
  react.useEffect(function () {
    lastSnapshot.current = currSnapshot;
    lastAffected.current = currAffected;
  });

  if (process.env.NODE_ENV !== "production") {
    useAffectedDebugValue(currSnapshot, currAffected);
  }

  var proxyCache = react.useMemo(function () {
    return new WeakMap();
  }, []);
  return proxyCompare.createProxy(currSnapshot, currAffected, proxyCache);
}

Object.defineProperty(exports, 'getVersion', {
  enumerable: true,
  get: function () { return vanilla.getVersion; }
});
Object.defineProperty(exports, 'proxy', {
  enumerable: true,
  get: function () { return vanilla.proxy; }
});
Object.defineProperty(exports, 'ref', {
  enumerable: true,
  get: function () { return vanilla.ref; }
});
Object.defineProperty(exports, 'snapshot', {
  enumerable: true,
  get: function () { return vanilla.snapshot; }
});
Object.defineProperty(exports, 'subscribe', {
  enumerable: true,
  get: function () { return vanilla.subscribe; }
});
Object.defineProperty(exports, 'unstable_buildProxyFunction', {
  enumerable: true,
  get: function () { return vanilla.unstable_buildProxyFunction; }
});
exports.useSnapshot = useSnapshot;
