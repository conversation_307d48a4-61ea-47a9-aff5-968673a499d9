"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _QCG6J6X5cjs = require('../__chunks/QCG6J6X5.cjs');
require('../__chunks/LFHNPG2L.cjs');
require('../__chunks/5UTRYT6E.cjs');


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/DAJUUBUI.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');


var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-disclosure.tsx
var _misc = require('@ariakit/core/utils/misc');
var _jsxruntime = require('react/jsx-runtime');
var children = /* @__PURE__ */ _jsxruntime.jsx.call(void 0, 
  "svg",
  {
    "aria-hidden": "true",
    display: "block",
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "1.5pt",
    viewBox: "0 0 16 16",
    height: "1em",
    width: "1em",
    pointerEvents: "none",
    children: /* @__PURE__ */ _jsxruntime.jsx.call(void 0, "polyline", { points: "4,6 8,10 12,6" })
  }
);
var useComboboxDisclosure = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store"]);
    const context = _3WCBE6SUcjs.useComboboxProviderContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxDisclosure must receive a `store` prop or be wrapped in a ComboboxProvider component."
    );
    const onMouseDownProp = props.onMouseDown;
    const onMouseDown = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onMouseDownProp == null ? void 0 : onMouseDownProp(event);
      event.preventDefault();
      store == null ? void 0 : store.move(null);
    });
    const onClickProp = props.onClick;
    const onClick = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onClickProp == null ? void 0 : onClickProp(event);
      if (event.defaultPrevented)
        return;
      if (!store)
        return;
      const { baseElement } = store.getState();
      store.setDisclosureElement(baseElement);
    });
    const open = store.useState("open");
    props = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      children,
      tabIndex: -1,
      "aria-label": open ? "Hide popup" : "Show popup",
      "aria-expanded": open
    }, props), {
      onMouseDown,
      onClick
    });
    props = _QCG6J6X5cjs.useDialogDisclosure.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, { store }, props));
    return props;
  }
);
var ComboboxDisclosure = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxDisclosure(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "button", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxDisclosure.displayName = "ComboboxDisclosure";
}



exports.ComboboxDisclosure = ComboboxDisclosure; exports.useComboboxDisclosure = useComboboxDisclosure;
