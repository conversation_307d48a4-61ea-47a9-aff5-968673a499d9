const icon = `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 294 524"><defs><linearGradient id="a" x1="36.742%" x2="37.116%" y1="100.518%" y2="-.001%"><stop offset="0%" stop-color="%23DD5F29"/><stop offset="26.042%" stop-color="%23F26B32"/><stop offset="100%" stop-color="%23FAC932"/></linearGradient><linearGradient id="b" x1="28.046%" x2="28.421%" y1="100.518%" y2="-.003%"><stop offset="0%" stop-color="%23DD5F29"/><stop offset="26.042%" stop-color="%23F26B32"/><stop offset="100%" stop-color="%23FAC932"/></linearGradient><linearGradient id="c" x1="38.215%" x2="38.589%" y1="100.518%" y2="0%"><stop offset="0%" stop-color="%23DD5F29"/><stop offset="26.042%" stop-color="%23F26B32"/><stop offset="100%" stop-color="%23FAC932"/></linearGradient></defs><g fill="none" fill-rule="evenodd"><path fill="url(%23a)" d="M218.617 270.615c-9.752 0-18.896-5.689-23.366-14.63l-7.72-17.27h-76.6l-7.722 17.27c-4.47 8.941-13.613 14.63-23.366 14.63H75.78l32.712 249.306c1.625 4.671 4.673 4.671 6.502 0l32.51-79.648 28.242 79.442c1.625 4.676 4.673 4.676 6.501 0L220.04 270.82l-1.423-.204Z" transform="translate(-1.58 -.2)"/><path fill="url(%23b)" d="M184.47 231.784h-70.3l-10.77 24.179c-3.657 7.314-10.768 12.597-18.489 14.02L109.7 423.791c1.625 2.844 4.673 2.844 6.501 0l31.697-48.155 29.055 47.951c1.829 2.845 4.673 2.845 6.502 0l28.039-154.012c-6.908-2.032-13.004-6.908-16.255-13.613l-10.768-24.18Z" transform="translate(-1.58 -.2)"/><path fill="url(%23c)" d="m195.259 255.988-46.123-103.014-45.92 103.014c-1.625 3.048-3.656 5.69-6.095 7.925l19.1 102.2c1.015 1.423 3.657 1.83 5.485 0l25.601-33.931 25.602 33.728c1.625 2.032 4.47 1.626 5.485 0l21.131-103.42c-1.625-2.032-3.047-4.064-4.266-6.502Z" transform="translate(-1.58 -.2)"/><path fill="%23F56F46" d="M.439 12.559c-1.422-4.877 1.422-8.33 6.299-8.33H47.17c2.845 0 5.486 2.437 6.299 4.876l29.665 116.83h1.422l53.437-121.3c1.016-2.032 3.048-3.86 5.892-3.86h6.299c3.047 0 5.08 1.625 5.892 3.86l53.437 121.3h1.423L240.6 9.105c.61-2.439 3.454-4.877 6.299-4.877h40.433c4.877 0 7.518 3.454 6.299 8.33l-65.221 231.63c-.61 2.845-3.454 4.876-6.298 4.876h-5.487c-2.438 0-4.876-1.625-5.892-3.86l-63.19-141.009h-1.015L83.744 245.203c-1.016 2.032-3.454 3.86-5.892 3.86h-5.486c-2.845 0-5.486-2.031-6.299-4.876L.44 12.559Z"/></g></svg>`;

const UIStrings = {
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Remove Unused CSS is a name of the feature */
  'unused-css-rules': `Enable [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) in 'WP Rocket' to fix this issue. It reduces page size by removing all CSS and stylesheets that are not used while keeping only the used CSS for each page.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. `Imagify` is an image optimization add-on */
  'modern-image-formats': `Enable 'Imagify' from the Image Optimization tab in 'WP Rocket' to convert your images to WebP.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Delay JavaScript, LazyLoad for iframes and videos and Replace YouTube iframe with preview image are names of the features */
  'unused-javascript': `Enable [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) in 'WP Rocket' to fix this problem. It will improve the loading of your page by delaying the execution of scripts until user interaction. If your site has iframes, you can use WP Rocket's [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) and [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) as well.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Remove Unused CSS, Load JavaScript deferred are names of the features */
  'render-blocking-resources': `Enable [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) and [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) in 'WP Rocket' to address this recommendation. These features will respectively optimize the CSS and JavaScript files so that they don't block the rendering of your page.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Minify CSS Files is a name of the feature */
  'unminified-css': `Enable [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) in 'WP Rocket' to fix this issue. Any spaces and comments in your site's CSS files will be removed to make the file size smaller and faster to download.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Minify JavaScript Files is a name of the feature */
  'unminified-javascript': `Enable [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) in 'WP Rocket' to fix this issue. Empty spaces and comments will be removed from JavaScript files to make their size smaller and faster to download.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. `Imagify` is an image optimization add-on */
  'uses-optimized-images': `Enable 'Imagify' from the Image Optimization tab in 'WP Rocket' and run Bulk Optimization to compress your images.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Prefetch DNS Requests, Enable CDN are names of the features */
  'uses-rel-preconnect': `Use [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) in 'WP Rocket' to add "dns-prefetch" and speed up the connection with external domains. Also, 'WP Rocket' automatically adds "preconnect" to [Google Fonts domain](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) and any CNAME(S) added via the [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) feature.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. Remove Unused CSS is a name of the feature*/
  'uses-rel-preload': `To fix this issue for fonts, enable [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) in 'WP Rocket'. Your site's critical fonts will be preloaded with priority.`,
  /** Additional description of a Lighthouse audit for a third-party framework called 'WP Rocket'. This is displayed after a user expands the section to see more. No character length limits. LazyLoad for images is a name of the feature*/
  'offscreen-images': `Enable [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) in WP Rocket to fix this recommendation. This feature delays the loading of the images until the visitor scrolls down the page and actually needs to see them.`,
};

module.exports = {
  id: 'wp-rocket',
  title: 'WP Rocket',
  icon,
  UIStrings,
};
