---
path: /docs/role/
---

# Role

`Role` is the most abstract component on top of which all other Reakit components are built. It's inspired by the [WAI-ARIA RoleType Role](https://www.w3.org/TR/wai-aria-1.1/#roletype). By default, it renders a `div` element.

<carbon-ad></carbon-ad>

## Installation

```sh
npm install reakit
```

Learn more in [Get started](/docs/get-started/).

## Usage

```jsx
import { Role } from "reakit/Role";

function Example() {
  return <Role>Role</Role>;
}
```

### `as` prop

Learn more about the `as` prop in [Composition](/docs/composition/#as-prop).

```jsx
import { Role } from "reakit/Role";

function Example() {
  return <Role as="button">Button</Role>;
}
```

### Render props

Learn more about render props in [Composition](/docs/composition/#render-props).

```jsx
import { Role } from "reakit/Role";

function Example() {
  return <Role>{(props) => <button {...props}>Button</button>}</Role>;
}
```

### Props hooks

Learn more about props hooks in [Composition](/docs/composition/#props-hooks).

```jsx
import { useButton } from "reakit/Button";
import { Role } from "reakit/Role";

function Example() {
  const buttonProps = useButton();
  return (
    <Role {...buttonProps} as="button">
      Button
    </Role>
  );
}
```

## Composition

- `Role` is used by all Reakit components.

Learn more in [Composition](/docs/composition/#props-hooks).

## Props

<!-- Automatically generated -->

### `Role`

No props to show
