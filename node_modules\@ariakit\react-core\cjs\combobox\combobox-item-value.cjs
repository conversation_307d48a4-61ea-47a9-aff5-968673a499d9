"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";



var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');
require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-item-value.tsx
var _react = require('react');
var _misc = require('@ariakit/core/utils/misc');
var _jsxruntime = require('react/jsx-runtime');
function normalizeValue(value) {
  return _misc.normalizeString.call(void 0, value).toLowerCase();
}
function splitValue(itemValue, userValue) {
  userValue = normalizeValue(userValue);
  let index = normalizeValue(itemValue).indexOf(userValue);
  const parts = [];
  while (index !== -1) {
    if (index !== 0) {
      parts.push(
        /* @__PURE__ */ _jsxruntime.jsx.call(void 0, "span", { "data-autocomplete-value": "", children: itemValue.substr(0, index) }, parts.length)
      );
    }
    parts.push(
      /* @__PURE__ */ _jsxruntime.jsx.call(void 0, "span", { "data-user-value": "", children: itemValue.substr(index, userValue.length) }, parts.length)
    );
    itemValue = itemValue.substr(index + userValue.length);
    index = normalizeValue(itemValue).indexOf(userValue);
  }
  if (itemValue) {
    parts.push(
      /* @__PURE__ */ _jsxruntime.jsx.call(void 0, "span", { "data-autocomplete-value": "", children: itemValue }, parts.length)
    );
  }
  return parts;
}
var useComboboxItemValue = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, { store, value } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, ["store", "value"]);
    const context = _3WCBE6SUcjs.useComboboxScopedContext.call(void 0, );
    store = store || context;
    const itemContext = _react.useContext.call(void 0, _3WCBE6SUcjs.ComboboxItemValueContext);
    const itemValue = value != null ? value : itemContext;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "ComboboxItemValue must be wrapped in a ComboboxItem component."
    );
    const stateValue = store.useState(
      (state) => itemValue && state.value ? state.value : void 0
    );
    const children = _react.useMemo.call(void 0, 
      () => itemValue && stateValue ? splitValue(itemValue, stateValue) : itemValue,
      [itemValue, stateValue]
    );
    props = _AV6KTKLEcjs.__spreadValues.call(void 0, {
      children
    }, props);
    return props;
  }
);
var ComboboxItemValue = _RNZNGEL4cjs.createComponent.call(void 0, 
  (props) => {
    const htmlProps = useComboboxItemValue(props);
    return _RNZNGEL4cjs.createElement.call(void 0, "span", htmlProps);
  }
);
if (process.env.NODE_ENV !== "production") {
  ComboboxItemValue.displayName = "ComboboxItemValue";
}



exports.ComboboxItemValue = ComboboxItemValue; exports.useComboboxItemValue = useComboboxItemValue;
