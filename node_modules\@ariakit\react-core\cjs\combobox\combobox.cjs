"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _3QW6ELTGcjs = require('../__chunks/3QW6ELTG.cjs');


var _3WCBE6SUcjs = require('../__chunks/3WCBE6SU.cjs');


var _H6OLBVHAcjs = require('../__chunks/H6OLBVHA.cjs');
require('../__chunks/6ZZYUFPW.cjs');
require('../__chunks/UZNYSPKP.cjs');
require('../__chunks/BZTDJIVT.cjs');
require('../__chunks/UVBBMANL.cjs');
require('../__chunks/F2A2ZQDB.cjs');
require('../__chunks/S6UU7NA4.cjs');
require('../__chunks/Z3GCTNW4.cjs');
require('../__chunks/75KXQZJX.cjs');




var _RNZNGEL4cjs = require('../__chunks/RNZNGEL4.cjs');









var _EO6LS72Hcjs = require('../__chunks/EO6LS72H.cjs');
require('../__chunks/CJDHQUBR.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox.ts
var _react = require('react');




var _dom = require('@ariakit/core/utils/dom');



var _events = require('@ariakit/core/utils/events');
var _focus = require('@ariakit/core/utils/focus');




var _misc = require('@ariakit/core/utils/misc');
var _reactdom = require('react-dom');
function isFirstItemAutoSelected(items, activeValue, autoSelect) {
  if (!autoSelect)
    return false;
  const firstItem = items.find((item) => !item.disabled && item.value);
  return (firstItem == null ? void 0 : firstItem.value) === activeValue;
}
function hasCompletionString(value, activeValue) {
  if (!activeValue)
    return false;
  if (value == null)
    return false;
  value = _misc.normalizeString.call(void 0, value);
  return activeValue.length > value.length && activeValue.toLowerCase().indexOf(value.toLowerCase()) === 0;
}
function isInputEvent(event) {
  return event.type === "input";
}
function isAriaAutoCompleteValue(value) {
  return value === "inline" || value === "list" || value === "both" || value === "none";
}
var useCombobox = _RNZNGEL4cjs.createHook.call(void 0, 
  (_a) => {
    var _b = _a, {
      store,
      focusable = true,
      autoSelect: autoSelectProp = false,
      getAutoSelectId,
      showOnChange = true,
      setValueOnChange = true,
      showOnMouseDown = true,
      setValueOnClick = true,
      showOnKeyDown = true,
      moveOnKeyPress = true,
      autoComplete = "list"
    } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
      "store",
      "focusable",
      "autoSelect",
      "getAutoSelectId",
      "showOnChange",
      "setValueOnChange",
      "showOnMouseDown",
      "setValueOnClick",
      "showOnKeyDown",
      "moveOnKeyPress",
      "autoComplete"
    ]);
    const context = _3WCBE6SUcjs.useComboboxProviderContext.call(void 0, );
    store = store || context;
    _misc.invariant.call(void 0, 
      store,
      process.env.NODE_ENV !== "production" && "Combobox must receive a `store` prop or be wrapped in a ComboboxProvider component."
    );
    const ref = _react.useRef.call(void 0, null);
    const [valueUpdated, forceValueUpdate] = _EO6LS72Hcjs.useForceUpdate.call(void 0, );
    const canAutoSelectRef = _react.useRef.call(void 0, false);
    const composingRef = _react.useRef.call(void 0, false);
    const autoSelect = store.useState(
      (state) => !!autoSelectProp && state.virtualFocus
    );
    const inline = autoComplete === "inline" || autoComplete === "both";
    const [canInline, setCanInline] = _react.useState.call(void 0, inline);
    _EO6LS72Hcjs.useUpdateLayoutEffect.call(void 0, () => {
      if (!inline)
        return;
      setCanInline(true);
    }, [inline]);
    const storeValue = store.useState("value");
    const activeValue = store.useState(
      (state) => inline && canInline ? state.activeValue : void 0
    );
    const items = store.useState("renderedItems");
    const open = store.useState("open");
    const contentElement = store.useState("contentElement");
    const value = _react.useMemo.call(void 0, () => {
      if (!inline)
        return storeValue;
      if (!canInline)
        return storeValue;
      const firstItemAutoSelected = isFirstItemAutoSelected(
        items,
        activeValue,
        autoSelect
      );
      if (firstItemAutoSelected) {
        if (hasCompletionString(storeValue, activeValue)) {
          const slice = (activeValue == null ? void 0 : activeValue.slice(storeValue.length)) || "";
          return storeValue + slice;
        }
        return storeValue;
      }
      return activeValue || storeValue;
    }, [inline, canInline, items, activeValue, autoSelect, storeValue]);
    _react.useEffect.call(void 0, () => {
      const element = ref.current;
      if (!element)
        return;
      const onCompositeItemMove = () => setCanInline(true);
      element.addEventListener("combobox-item-move", onCompositeItemMove);
      return () => {
        element.removeEventListener("combobox-item-move", onCompositeItemMove);
      };
    }, []);
    _react.useEffect.call(void 0, () => {
      if (!inline)
        return;
      if (!canInline)
        return;
      if (!activeValue)
        return;
      const firstItemAutoSelected = isFirstItemAutoSelected(
        items,
        activeValue,
        autoSelect
      );
      if (!firstItemAutoSelected)
        return;
      if (!hasCompletionString(storeValue, activeValue))
        return;
      queueMicrotask(() => {
        const element = ref.current;
        if (!element)
          return;
        _dom.setSelectionRange.call(void 0, element, storeValue.length, activeValue.length);
      });
    }, [
      valueUpdated,
      inline,
      canInline,
      activeValue,
      items,
      autoSelect,
      storeValue
    ]);
    const scrollingElementRef = _react.useRef.call(void 0, null);
    const getAutoSelectIdProp = _EO6LS72Hcjs.useEvent.call(void 0, getAutoSelectId);
    const autoSelectIdRef = _react.useRef.call(void 0, null);
    _react.useEffect.call(void 0, () => {
      if (!open)
        return;
      if (!contentElement)
        return;
      const scrollingElement = _dom.getScrollingElement.call(void 0, contentElement);
      if (!scrollingElement)
        return;
      scrollingElementRef.current = scrollingElement;
      const onWheel = () => {
        canAutoSelectRef.current = false;
      };
      const onScroll = () => {
        if (!store)
          return;
        if (!canAutoSelectRef.current)
          return;
        const { activeId } = store.getState();
        if (activeId === null)
          return;
        if (activeId === autoSelectIdRef.current)
          return;
        canAutoSelectRef.current = false;
      };
      const options = { passive: true, capture: true };
      scrollingElement.addEventListener("wheel", onWheel, options);
      scrollingElement.addEventListener("scroll", onScroll, options);
      return () => {
        scrollingElement.removeEventListener("wheel", onWheel, true);
        scrollingElement.removeEventListener("scroll", onScroll, true);
      };
    }, [open, contentElement, store]);
    _EO6LS72Hcjs.useSafeLayoutEffect.call(void 0, () => {
      if (!storeValue)
        return;
      if (composingRef.current)
        return;
      canAutoSelectRef.current = true;
    }, [storeValue]);
    _EO6LS72Hcjs.useSafeLayoutEffect.call(void 0, () => {
      if (open)
        return;
      canAutoSelectRef.current = false;
    }, [open]);
    const resetValueOnSelect = store.useState("resetValueOnSelect");
    _EO6LS72Hcjs.useUpdateEffect.call(void 0, () => {
      var _a2;
      const canAutoSelect = canAutoSelectRef.current;
      if (!store)
        return;
      if ((!autoSelect || !canAutoSelect) && !resetValueOnSelect)
        return;
      const { baseElement, contentElement: contentElement2, activeId } = store.getState();
      if (baseElement && !_focus.hasFocus.call(void 0, baseElement))
        return;
      if (contentElement2 == null ? void 0 : contentElement2.hasAttribute("data-placing")) {
        const observer = new MutationObserver(forceValueUpdate);
        observer.observe(contentElement2, { attributeFilter: ["data-placing"] });
        return () => observer.disconnect();
      }
      if (autoSelect && canAutoSelect) {
        const userAutoSelectId = getAutoSelectIdProp(items);
        const autoSelectId = userAutoSelectId !== void 0 ? userAutoSelectId : store.first();
        autoSelectIdRef.current = autoSelectId;
        store.move(autoSelectId != null ? autoSelectId : null);
      } else {
        const element = (_a2 = store.item(activeId)) == null ? void 0 : _a2.element;
        if (element && "scrollIntoView" in element) {
          element.scrollIntoView({ block: "nearest", inline: "nearest" });
        }
      }
      return;
    }, [
      store,
      valueUpdated,
      storeValue,
      autoSelect,
      resetValueOnSelect,
      getAutoSelectIdProp,
      items
    ]);
    _react.useEffect.call(void 0, () => {
      if (!inline)
        return;
      const combobox = ref.current;
      if (!combobox)
        return;
      const elements = [combobox, contentElement].filter(
        (value2) => !!value2
      );
      const onBlur2 = (event) => {
        if (elements.every((el) => _events.isFocusEventOutside.call(void 0, event, el))) {
          store == null ? void 0 : store.setValue(value);
        }
      };
      elements.forEach((el) => el.addEventListener("focusout", onBlur2));
      return () => {
        elements.forEach((el) => el.removeEventListener("focusout", onBlur2));
      };
    }, [inline, contentElement, store, value]);
    const onChangeProp = props.onChange;
    const showOnChangeProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, showOnChange);
    const setValueOnChangeProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, setValueOnChange);
    const onChange = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onChangeProp == null ? void 0 : onChangeProp(event);
      if (event.defaultPrevented)
        return;
      if (!store)
        return;
      const { value: value2, selectionStart, selectionEnd } = event.target;
      const nativeEvent = event.nativeEvent;
      canAutoSelectRef.current = true;
      if (isInputEvent(nativeEvent)) {
        if (nativeEvent.isComposing) {
          canAutoSelectRef.current = false;
          composingRef.current = true;
        }
        if (inline) {
          const textInserted = nativeEvent.inputType === "insertText" || nativeEvent.inputType === "insertCompositionText";
          const caretAtEnd = selectionStart === value2.length;
          setCanInline(textInserted && caretAtEnd);
        }
      }
      if (setValueOnChangeProp(event)) {
        const isSameValue = value2 === store.getState().value;
        _reactdom.flushSync.call(void 0, () => store == null ? void 0 : store.setValue(value2));
        _dom.setSelectionRange.call(void 0, event.currentTarget, selectionStart, selectionEnd);
        if (inline && autoSelect && isSameValue) {
          forceValueUpdate();
        }
      }
      if (showOnChangeProp(event)) {
        store.show();
      }
      if (!autoSelect || !canAutoSelectRef.current) {
        store.setActiveId(null);
      }
    });
    const onCompositionEndProp = props.onCompositionEnd;
    const onCompositionEnd = _EO6LS72Hcjs.useEvent.call(void 0, 
      (event) => {
        canAutoSelectRef.current = true;
        composingRef.current = false;
        onCompositionEndProp == null ? void 0 : onCompositionEndProp(event);
        if (event.defaultPrevented)
          return;
        if (!autoSelect)
          return;
        forceValueUpdate();
      }
    );
    const onMouseDownProp = props.onMouseDown;
    const setValueOnClickProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, setValueOnClick);
    const showOnMouseDownProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, showOnMouseDown);
    const onMouseDown = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      onMouseDownProp == null ? void 0 : onMouseDownProp(event);
      if (event.defaultPrevented)
        return;
      if (event.button)
        return;
      if (event.ctrlKey)
        return;
      if (!store)
        return;
      store.setActiveId(null);
      if (setValueOnClickProp(event)) {
        store.setValue(value);
      }
      if (showOnMouseDownProp(event)) {
        _events.queueBeforeEvent.call(void 0, event.currentTarget, "mouseup", store.show);
      }
    });
    const onKeyDownProp = props.onKeyDown;
    const showOnKeyDownProp = _EO6LS72Hcjs.useBooleanEvent.call(void 0, showOnKeyDown);
    const onKeyDown = _EO6LS72Hcjs.useEvent.call(void 0, 
      (event) => {
        onKeyDownProp == null ? void 0 : onKeyDownProp(event);
        if (!event.repeat) {
          canAutoSelectRef.current = false;
        }
        if (event.defaultPrevented)
          return;
        if (event.ctrlKey)
          return;
        if (event.altKey)
          return;
        if (event.shiftKey)
          return;
        if (event.metaKey)
          return;
        if (!store)
          return;
        const { open: open2, activeId } = store.getState();
        if (open2)
          return;
        if (activeId !== null)
          return;
        if (event.key === "ArrowUp" || event.key === "ArrowDown") {
          if (showOnKeyDownProp(event)) {
            event.preventDefault();
            store.show();
          }
        }
      }
    );
    const onBlurProp = props.onBlur;
    const onBlur = _EO6LS72Hcjs.useEvent.call(void 0, (event) => {
      canAutoSelectRef.current = false;
      onBlurProp == null ? void 0 : onBlurProp(event);
      if (event.defaultPrevented)
        return;
    });
    const id = _EO6LS72Hcjs.useId.call(void 0, props.id);
    const ariaAutoComplete = isAriaAutoCompleteValue(autoComplete) ? autoComplete : void 0;
    const isActiveItem = store.useState((state) => state.activeId === null);
    props = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      id,
      role: "combobox",
      "aria-autocomplete": ariaAutoComplete,
      "aria-haspopup": _dom.getPopupRole.call(void 0, contentElement, "listbox"),
      "aria-expanded": open,
      "aria-controls": contentElement == null ? void 0 : contentElement.id,
      "data-active-item": isActiveItem || void 0,
      value
    }, props), {
      ref: _EO6LS72Hcjs.useMergeRefs.call(void 0, ref, props.ref),
      onChange,
      onCompositionEnd,
      onMouseDown,
      onKeyDown,
      onBlur
    });
    props = _H6OLBVHAcjs.useComposite.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {
      store,
      focusable
    }, props), {
      // Enable inline autocomplete when the user moves from the combobox input
      // to an item.
      moveOnKeyPress: (event) => {
        if (_misc.isFalsyBooleanCallback.call(void 0, moveOnKeyPress, event))
          return false;
        if (inline)
          setCanInline(true);
        return true;
      }
    }));
    props = _3QW6ELTGcjs.usePopoverAnchor.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, { store }, props));
    return _AV6KTKLEcjs.__spreadValues.call(void 0, { autoComplete: "off" }, props);
  }
);
var Combobox = _RNZNGEL4cjs.createComponent.call(void 0, (props) => {
  const htmlProps = useCombobox(props);
  return _RNZNGEL4cjs.createElement.call(void 0, "input", htmlProps);
});
if (process.env.NODE_ENV !== "production") {
  Combobox.displayName = "Combobox";
}



exports.Combobox = Combobox; exports.useCombobox = useCombobox;
