"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _TQTGWD5Kcjs = require('../__chunks/TQTGWD5K.cjs');
require('../__chunks/E53JW5BD.cjs');
require('../__chunks/I3Y4EXEF.cjs');
require('../__chunks/OTVLDMN2.cjs');


var _BFE5R4EZcjs = require('../__chunks/BFE5R4EZ.cjs');
require('../__chunks/MI6NUQYQ.cjs');








var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');



var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');
require('../__chunks/5F4DVUNS.cjs');
require('../__chunks/ULSPM3Y3.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/menu/menu-store.ts
function createMenuStore(_a = {}) {
  var _b = _a, {
    combobox,
    parent,
    menubar
  } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
    "combobox",
    "parent",
    "menubar"
  ]);
  const parentIsMenubar = !!menubar && !parent;
  const store = _F6HPKLO2cjs.mergeStore.call(void 0, 
    props.store,
    _F6HPKLO2cjs.pick.call(void 0, parent, ["values"]),
    _F6HPKLO2cjs.omit.call(void 0, combobox, [
      "arrowElement",
      "anchorElement",
      "contentElement",
      "popoverElement",
      "disclosureElement"
    ])
  );
  _F6HPKLO2cjs.throwOnConflictingProps.call(void 0, props, store);
  const syncState = store.getState();
  const composite = _BFE5R4EZcjs.createCompositeStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    store,
    orientation: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.orientation,
      syncState.orientation,
      "vertical"
    )
  }));
  const hovercard = _TQTGWD5Kcjs.createHovercardStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    store,
    placement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.placement,
      syncState.placement,
      "bottom-start"
    ),
    timeout: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.timeout,
      syncState.timeout,
      parentIsMenubar ? 0 : 150
    ),
    hideTimeout: _KBNYGXWIcjs.defaultValue.call(void 0, props.hideTimeout, syncState.hideTimeout, 0)
  }));
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite.getState()), hovercard.getState()), {
    initialFocus: _KBNYGXWIcjs.defaultValue.call(void 0, syncState.initialFocus, "container"),
    values: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.values,
      syncState.values,
      props.defaultValues,
      {}
    )
  });
  const menu = _F6HPKLO2cjs.createStore.call(void 0, initialState, composite, hovercard, store);
  _F6HPKLO2cjs.setup.call(void 0, 
    menu,
    () => _F6HPKLO2cjs.sync.call(void 0, menu, ["mounted"], (state) => {
      if (state.mounted)
        return;
      menu.setState("activeId", null);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    menu,
    () => _F6HPKLO2cjs.sync.call(void 0, parent, ["orientation"], (state) => {
      menu.setState(
        "placement",
        state.orientation === "vertical" ? "right-start" : "bottom-start"
      );
    })
  );
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite), hovercard), menu), {
    combobox,
    parent,
    menubar,
    hideAll: () => {
      hovercard.hide();
      parent == null ? void 0 : parent.hideAll();
    },
    setInitialFocus: (value) => menu.setState("initialFocus", value),
    setValues: (values) => menu.setState("values", values),
    setValue: (name, value) => {
      if (name === "__proto__")
        return;
      if (name === "constructor")
        return;
      if (Array.isArray(name))
        return;
      menu.setState("values", (values) => {
        const prevValue = values[name];
        const nextValue = _KBNYGXWIcjs.applyState.call(void 0, value, prevValue);
        if (nextValue === prevValue)
          return values;
        return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, values), {
          [name]: nextValue !== void 0 && nextValue
        });
      });
    }
  });
}


exports.createMenuStore = createMenuStore;
