import type { HovercardStoreFunctions, HovercardStoreOptions, HovercardStoreState } from "../hovercard/hovercard-store.js";
import type { Store, StoreOptions, StoreProps } from "../utils/store.js";
/**
 * Creates a tooltip store.
 */
export declare function createTooltipStore(props?: TooltipStoreProps): TooltipStore;
export interface TooltipStoreState extends HovercardStoreState {
    /** @default "top" */
    placement: HovercardStoreState["placement"];
    /** @default 0 */
    hideTimeout?: HovercardStoreState["hideTimeout"];
    /**
     * Determines whether the tooltip is being used as a label or a description
     * for the anchor element.
     * @deprecated Render a visually hidden label or use the `aria-label` or
     * `aria-labelledby` attributes on the anchor element instead.
     * @default "description"
     */
    type: "label" | "description";
    /**
     * The amount of time after a tooltip is hidden while all tooltips on the
     * page can be shown immediately, without waiting for the show timeout.
     * @default 300
     */
    skipTimeout: number;
}
export type TooltipStoreFunctions = HovercardStoreFunctions;
export interface TooltipStoreOptions extends HovercardStoreOptions, StoreOptions<TooltipStoreState, "type" | "placement" | "timeout" | "showTimeout" | "hideTimeout" | "skipTimeout"> {
}
export interface TooltipStoreProps extends TooltipStoreOptions, StoreProps<TooltipStoreState> {
}
export interface TooltipStore extends TooltipStoreFunctions, Store<TooltipStoreState> {
}
