{"name": "valtio", "private": false, "version": "1.7.0", "description": "💊 Val<PERSON><PERSON> makes proxy-state simple for React and Vanilla", "main": "./index.js", "types": "./index.d.ts", "typesVersions": {"<4.0": {"esm/*": ["ts3.4/*"], "*": ["ts3.4/*"]}}, "exports": {"./package.json": "./package.json", ".": {"types": "./index.d.ts", "module": "./esm/index.js", "import": "./esm/index.mjs", "default": "./index.js"}, "./vanilla": {"types": "./vanilla.d.ts", "module": "./esm/vanilla.js", "import": "./esm/vanilla.mjs", "default": "./vanilla.js"}, "./utils": {"types": "./utils.d.ts", "module": "./esm/utils.js", "import": "./esm/utils.mjs", "default": "./utils.js"}, "./macro": {"types": "./macro.d.ts", "module": "./esm/macro.js", "import": "./esm/macro.mjs", "default": "./macro.js"}, "./macro/vite": {"types": "./esm/macro/vite.d.ts", "module": "./esm/macro/vite.js", "import": "./esm/macro/vite.mjs", "default": "./macro/vite.js"}}, "files": ["**"], "sideEffects": false, "engines": {"node": ">=12.7.0"}, "repository": {"type": "git", "url": "git+https://github.com/pmndrs/valtio.git"}, "keywords": ["react", "state", "manager", "management", "mobx", "proxy", "store"], "author": "<PERSON><PERSON>", "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/pmndrs/valtio/issues"}, "homepage": "https://github.com/pmndrs/valtio", "dependencies": {"proxy-compare": "2.3.0", "use-sync-external-store": "1.2.0"}, "resolutions": {"date-fns": "2.27.0"}, "peerDependencies": {"@babel/helper-module-imports": ">=7.12", "@babel/types": ">=7.13", "aslemammad-vite-plugin-macro": ">=1.0.0-alpha.1", "babel-plugin-macros": ">=3.0", "react": ">=16.8", "vite": ">=2.8.6"}, "peerDependenciesMeta": {"vite": {"optional": true}, "aslemammad-vite-plugin-macro": {"optional": true}, "@babel/helper-module-imports": {"optional": true}, "@babel/types": {"optional": true}, "babel-plugin-macros": {"optional": true}, "react": {"optional": true}}}