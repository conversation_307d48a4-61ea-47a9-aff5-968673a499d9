System.register(["@babel/helper-module-imports","@babel/types","aslemammad-vite-plugin-macro","babel-plugin-macros"],function(o){"use strict";var l,t,n,u;return{setters:[function(e){l=e},function(e){t=e},function(e){n=e},function(e){u=e}],execute:function(){o("provideValtioMacro",s);const{defineMacro:e,defineMacroProvider:b,createMacroPlugin:h}="default"in n?n.default:n,M=o("valtioMacro",e("useProxy").withSignature("<T extends object>(proxyObject: T): void").withHandler(g=>{var d,m,v,f;const{path:r,args:x}=g,P=l.addNamed(r,"useSnapshot","valtio"),a=(d=x[0])==null?void 0:d.node;if(!t.isIdentifier(a))throw new u.MacroError("no proxy object");const p=t.identifier(`valtio_macro_snap_${a.name}`);(m=r.parentPath)==null||m.replaceWith(t.variableDeclaration("const",[t.variableDeclarator(p,t.callExpression(P,[a]))]));let i=0;(f=(v=r.parentPath)==null?void 0:v.getFunctionParent())==null||f.traverse({Identifier(c){i===0&&c.node!==a&&c.node.name===a.name&&(c.node.name=p.name)},Function:{enter(){++i},exit(){--i}}})}));function s(){return b({id:"valtio/macro",exports:{"valtio/macro":{macros:[M]}}})}const y=o("default",h({}).use(s()))}}});
