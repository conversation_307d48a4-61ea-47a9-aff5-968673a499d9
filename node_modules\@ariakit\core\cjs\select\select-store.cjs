"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _E53JW5BDcjs = require('../__chunks/E53JW5BD.cjs');
require('../__chunks/I3Y4EXEF.cjs');
require('../__chunks/OTVLDMN2.cjs');


var _BFE5R4EZcjs = require('../__chunks/BFE5R4EZ.cjs');
require('../__chunks/MI6NUQYQ.cjs');








var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');
require('../__chunks/5F4DVUNS.cjs');


var _ULSPM3Y3cjs = require('../__chunks/ULSPM3Y3.cjs');




var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/select/select-store.ts
function createSelectStore(_a = {}) {
  var _b = _a, {
    combobox
  } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
    "combobox"
  ]);
  const store = _F6HPKLO2cjs.mergeStore.call(void 0, 
    props.store,
    _F6HPKLO2cjs.omit.call(void 0, combobox, [
      "value",
      "items",
      "renderedItems",
      "baseElement",
      "arrowElement",
      "anchorElement",
      "contentElement",
      "popoverElement",
      "disclosureElement"
    ])
  );
  _F6HPKLO2cjs.throwOnConflictingProps.call(void 0, props, store);
  const syncState = store.getState();
  const composite = _BFE5R4EZcjs.createCompositeStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    store,
    virtualFocus: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.virtualFocus,
      syncState.virtualFocus,
      true
    ),
    includesBaseElement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.includesBaseElement,
      syncState.includesBaseElement,
      false
    ),
    activeId: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.activeId,
      syncState.activeId,
      props.defaultActiveId,
      null
    ),
    orientation: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.orientation,
      syncState.orientation,
      "vertical"
    )
  }));
  const popover = _E53JW5BDcjs.createPopoverStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    store,
    placement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.placement,
      syncState.placement,
      "bottom-start"
    )
  }));
  const initialValue = new String("");
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite.getState()), popover.getState()), {
    value: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.value,
      syncState.value,
      props.defaultValue,
      initialValue
    ),
    setValueOnMove: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.setValueOnMove,
      syncState.setValueOnMove,
      false
    ),
    selectElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState.selectElement, null),
    labelElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState.labelElement, null)
  });
  const select = _F6HPKLO2cjs.createStore.call(void 0, initialState, composite, popover, store);
  _F6HPKLO2cjs.setup.call(void 0, 
    select,
    () => _F6HPKLO2cjs.sync.call(void 0, select, ["value", "items"], (state) => {
      if (state.value !== initialValue)
        return;
      if (!state.items.length)
        return;
      const item = state.items.find(
        (item2) => !item2.disabled && item2.value != null
      );
      if ((item == null ? void 0 : item.value) == null)
        return;
      select.setState("value", item.value);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    select,
    () => _F6HPKLO2cjs.sync.call(void 0, select, ["mounted", "items", "value"], (state) => {
      if (combobox)
        return;
      if (state.mounted)
        return;
      const values = _ULSPM3Y3cjs.toArray.call(void 0, state.value);
      const lastValue = values[values.length - 1];
      if (lastValue == null)
        return;
      const item = state.items.find(
        (item2) => !item2.disabled && item2.value === lastValue
      );
      if (!item)
        return;
      select.setState("activeId", item.id);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    select,
    () => _F6HPKLO2cjs.batch.call(void 0, select, ["setValueOnMove", "moves"], (state) => {
      const { mounted, value, activeId } = select.getState();
      if (!state.setValueOnMove && mounted)
        return;
      if (Array.isArray(value))
        return;
      if (!state.moves)
        return;
      if (!activeId)
        return;
      const item = composite.item(activeId);
      if (!item || item.disabled || item.value == null)
        return;
      select.setState("value", item.value);
    })
  );
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite), popover), select), {
    combobox,
    setValue: (value) => select.setState("value", value),
    setSelectElement: (element) => select.setState("selectElement", element),
    setLabelElement: (element) => select.setState("labelElement", element)
  });
}


exports.createSelectStore = createSelectStore;
