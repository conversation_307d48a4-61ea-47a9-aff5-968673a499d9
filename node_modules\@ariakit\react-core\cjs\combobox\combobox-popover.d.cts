import type { PopoverOptions } from "../popover/popover.js";
import type { As, Props } from "../utils/types.js";
import type { ComboboxListOptions } from "./combobox-list.js";
/**
 * Returns props to create a `ComboboxPopover` component.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore();
 * const props = useComboboxPopover({ store });
 * <Role {...props}>
 *   <ComboboxItem value="Item 1" />
 *   <ComboboxItem value="Item 2" />
 *   <ComboboxItem value="Item 3" />
 * </Role>
 * ```
 */
export declare const useComboboxPopover: import("../utils/types.js").Hook<ComboboxPopoverOptions<"div">>;
/**
 * Renders a combobox popover. The `role` prop is set to `listbox` by default,
 * but can be overriden by any other valid combobox popup role (`listbox`,
 * `menu`, `tree`, `grid` or `dialog`).
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {3-7}
 * <ComboboxProvider>
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Apple" />
 *     <ComboboxItem value="Banana" />
 *     <ComboboxItem value="Orange" />
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const ComboboxPopover: import("../utils/types.js").Component<ComboboxPopoverOptions<"div">>;
export interface ComboboxPopoverOptions<T extends As = "div"> extends ComboboxListOptions<T>, Omit<PopoverOptions<T>, "store"> {
}
export type ComboboxPopoverProps<T extends As = "div"> = Props<ComboboxPopoverOptions<T>>;
