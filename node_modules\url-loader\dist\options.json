{"type": "object", "properties": {"limit": {"description": "Enables/Disables transformation target file into base64 URIs (https://github.com/webpack-contrib/url-loader#limit).", "type": ["boolean", "number", "string"]}, "encoding": {"description": "Specify the encoding which the file will be in-lined with.", "oneOf": [{"type": "boolean"}, {"enum": ["utf8", "utf16le", "latin1", "base64", "hex", "ascii", "binary", "ucs2"]}]}, "mimetype": {"description": "The MIME type for the file to be transformed (https://github.com/webpack-contrib/url-loader#mimetype).", "oneOf": [{"type": "boolean"}, {"type": "string"}]}, "generator": {"description": "Adding custom implementation for encoding files.", "instanceof": "Function"}, "fallback": {"description": "An alternative loader to use when a target file's size exceeds the limit set in the limit option (https://github.com/webpack-contrib/url-loader#fallback).", "anyOf": [{"type": "string"}, {"additionalProperties": false, "properties": {"loader": {"description": "Fallback loader name.", "type": "string"}, "options": {"description": "Fallback loader options.", "anyOf": [{"type": "object"}, {"type": "string"}]}}, "type": "object"}]}, "esModule": {"description": "By default, url-loader generates JS modules that use the ES modules syntax.", "type": "boolean"}}, "additionalProperties": true}