{"name": "@ariakit/react", "version": "0.3.14", "description": "Toolkit for building accessible web apps with React", "sideEffects": false, "license": "MIT", "homepage": "https://ariakit.org", "type": "module", "main": "cjs/index.cjs", "module": "esm/index.js", "types": "cjs/index.d.ts", "repository": {"type": "git", "url": "https://github.com/ariakit/ariakit.git", "directory": "packages/ariakit-react"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/diegohaz"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ariakit"}, "scripts": {"lint": "eslint . --ext js,ts,tsx", "build": "node ../../scripts/build/build.js", "clean": "node ../../scripts/build/clean.js"}, "keywords": ["ariakit", "react", "a11y", "ui", "toolkit", "components"], "dependencies": {"@ariakit/react-core": "0.3.14"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "exports": {".": {"import": "./esm/index.js", "require": {"types": "./cjs/index.d.cts", "default": "./cjs/index.cjs"}}, "./visually-hidden": {"import": "./esm/visually-hidden.js", "require": {"types": "./cjs/visually-hidden.d.cts", "default": "./cjs/visually-hidden.cjs"}}, "./tooltip": {"import": "./esm/tooltip.js", "require": {"types": "./cjs/tooltip.d.cts", "default": "./cjs/tooltip.cjs"}}, "./toolbar": {"import": "./esm/toolbar.js", "require": {"types": "./cjs/toolbar.d.cts", "default": "./cjs/toolbar.cjs"}}, "./tab": {"import": "./esm/tab.js", "require": {"types": "./cjs/tab.d.cts", "default": "./cjs/tab.cjs"}}, "./separator": {"import": "./esm/separator.js", "require": {"types": "./cjs/separator.d.cts", "default": "./cjs/separator.cjs"}}, "./select": {"import": "./esm/select.js", "require": {"types": "./cjs/select.d.cts", "default": "./cjs/select.cjs"}}, "./role": {"import": "./esm/role.js", "require": {"types": "./cjs/role.d.cts", "default": "./cjs/role.cjs"}}, "./radio": {"import": "./esm/radio.js", "require": {"types": "./cjs/radio.d.cts", "default": "./cjs/radio.cjs"}}, "./portal": {"import": "./esm/portal.js", "require": {"types": "./cjs/portal.d.cts", "default": "./cjs/portal.cjs"}}, "./popover": {"import": "./esm/popover.js", "require": {"types": "./cjs/popover.d.cts", "default": "./cjs/popover.cjs"}}, "./menubar": {"import": "./esm/menubar.js", "require": {"types": "./cjs/menubar.d.cts", "default": "./cjs/menubar.cjs"}}, "./menu": {"import": "./esm/menu.js", "require": {"types": "./cjs/menu.d.cts", "default": "./cjs/menu.cjs"}}, "./hovercard": {"import": "./esm/hovercard.js", "require": {"types": "./cjs/hovercard.d.cts", "default": "./cjs/hovercard.cjs"}}, "./heading": {"import": "./esm/heading.js", "require": {"types": "./cjs/heading.d.cts", "default": "./cjs/heading.cjs"}}, "./group": {"import": "./esm/group.js", "require": {"types": "./cjs/group.d.cts", "default": "./cjs/group.cjs"}}, "./form": {"import": "./esm/form.js", "require": {"types": "./cjs/form.d.cts", "default": "./cjs/form.cjs"}}, "./focusable": {"import": "./esm/focusable.js", "require": {"types": "./cjs/focusable.d.cts", "default": "./cjs/focusable.cjs"}}, "./focus-trap": {"import": "./esm/focus-trap.js", "require": {"types": "./cjs/focus-trap.d.cts", "default": "./cjs/focus-trap.cjs"}}, "./disclosure": {"import": "./esm/disclosure.js", "require": {"types": "./cjs/disclosure.d.cts", "default": "./cjs/disclosure.cjs"}}, "./dialog": {"import": "./esm/dialog.js", "require": {"types": "./cjs/dialog.d.cts", "default": "./cjs/dialog.cjs"}}, "./composite": {"import": "./esm/composite.js", "require": {"types": "./cjs/composite.d.cts", "default": "./cjs/composite.cjs"}}, "./command": {"import": "./esm/command.js", "require": {"types": "./cjs/command.d.cts", "default": "./cjs/command.cjs"}}, "./combobox": {"import": "./esm/combobox.js", "require": {"types": "./cjs/combobox.d.cts", "default": "./cjs/combobox.cjs"}}, "./collection": {"import": "./esm/collection.js", "require": {"types": "./cjs/collection.d.cts", "default": "./cjs/collection.cjs"}}, "./checkbox": {"import": "./esm/checkbox.js", "require": {"types": "./cjs/checkbox.d.cts", "default": "./cjs/checkbox.cjs"}}, "./button": {"import": "./esm/button.js", "require": {"types": "./cjs/button.d.cts", "default": "./cjs/button.cjs"}}, "./package.json": "./package.json"}}