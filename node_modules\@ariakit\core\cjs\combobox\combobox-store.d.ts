import type { CompositeStoreFunctions, CompositeStoreItem, CompositeStoreOptions, CompositeStoreState } from "../composite/composite-store.js";
import type { PopoverStoreFunctions, PopoverStoreOptions, PopoverStoreState } from "../popover/popover-store.js";
import type { Store, StoreOptions, StoreProps } from "../utils/store.js";
import type { PickRequired, SetState } from "../utils/types.js";
type MutableValue<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue> = T extends string ? string : T;
/**
 * Creates a combobox store.
 */
export declare function createComboboxStore<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue>(props: PickRequired<ComboboxStoreProps<T>, "selectedValue" | "defaultSelectedValue">): ComboboxStore<T>;
export declare function createComboboxStore(props?: ComboboxStoreProps): ComboboxStore;
export type ComboboxStoreSelectedValue = string | string[];
export interface ComboboxStoreItem extends CompositeStoreItem {
    value?: string;
}
export interface ComboboxStoreState<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue> extends CompositeStoreState<ComboboxStoreItem>, PopoverStoreState {
    /**
     * @default true
     */
    includesBaseElement: boolean;
    /**
     * The combobox input value.
     *
     * Live examples:
     * - [Combobox with integrated
     *   filter](https://ariakit.org/examples/combobox-filtering-integrated)
     * - [Combobox with links](https://ariakit.org/examples/combobox-links)
     * - [Combobox filtering](https://ariakit.org/examples/combobox-filtering)
     * - [Multi-selectable
     *   Combobox](https://ariakit.org/examples/combobox-multiple)
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     */
    value: string;
    /**
     * The value of the currently active item. This state automatically updates as
     * the user navigates the combobox items either by keyboard or mouse click.
     * Note that it doesn't update when the user simply hovers over the items.
     */
    activeValue: string | undefined;
    /**
     * The value(s) of the currently selected item(s). This can be a string or an
     * array of strings. If it's an array, the combobox is considered
     * [multi-selectable](https://ariakit.org/examples/combobox-multiple).
     *
     * Live examples:
     * - [Multi-selectable
     *   Combobox](https://ariakit.org/examples/combobox-multiple)
     */
    selectedValue: MutableValue<T>;
    /**
     * Whether to reset the value when the combobox popover closes. This prop is
     * automatically set to `true` by default if the combobox supports multiple
     * selections. In other words, if the
     * [`selectedValue`](https://ariakit.org/reference/combobox-provider#selectedvalue)
     * or
     * [`defaultSelectedValue`](https://ariakit.org/reference/combobox-provider#defaultselectedvalue)
     * props are arrays.
     *
     * Live examples:
     * - [Multi-selectable
     *   Combobox](https://ariakit.org/examples/combobox-multiple)
     * - [Menu with Combobox](https://ariakit.org/examples/menu-combobox)
     * - [Select with Combobox](https://ariakit.org/examples/select-combobox)
     * - [Submenu with
     *   Combobox](https://ariakit.org/examples/menu-nested-combobox)
     */
    resetValueOnHide: boolean;
    /**
     * Whether to reset the value when an item is selected. This prop is
     * automatically set to `true` by default if the combobox supports multiple
     * selections. In other words, if the
     * [`selectedValue`](https://ariakit.org/reference/combobox-provider#selectedvalue)
     * or
     * [`defaultSelectedValue`](https://ariakit.org/reference/combobox-provider#defaultselectedvalue)
     * props are arrays.
     *
     * Live examples:
     * - [Multi-selectable
     *   Combobox](https://ariakit.org/examples/combobox-multiple)
     */
    resetValueOnSelect: boolean;
}
export interface ComboboxStoreFunctions<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue> extends CompositeStoreFunctions<ComboboxStoreItem>, PopoverStoreFunctions {
    /**
     * Sets the [`value`](https://ariakit.org/reference/combobox-provider#value)
     * state.
     *
     * Live examples:
     * - [Textarea with inline
     *   Combobox](https://ariakit.org/examples/combobox-textarea)
     * @example
     * store.setValue("Hello world");
     * store.setValue((value) => value + "!");
     */
    setValue: SetState<ComboboxStoreState<T>["value"]>;
    /**
     * Sets the
     * [`selectedValue`](https://ariakit.org/reference/combobox-provider#selectedvalue)
     * state.
     */
    setSelectedValue: SetState<ComboboxStoreState<T>["selectedValue"]>;
}
export interface ComboboxStoreOptions<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue> extends CompositeStoreOptions<ComboboxStoreItem>, PopoverStoreOptions, StoreOptions<ComboboxStoreState<T>, "includesBaseElement" | "value" | "selectedValue" | "resetValueOnHide" | "resetValueOnSelect"> {
    /**
     * @default null
     */
    defaultActiveId?: CompositeStoreOptions<ComboboxStoreItem>["activeId"];
    /**
     * The initial value of the combobox input.
     * @default ""
     */
    defaultValue?: ComboboxStoreState<T>["value"];
    /**
     * The initial value of the
     * [`selectedValue`](https://ariakit.org/reference/combobox-provider#selectedvalue)
     * state. This can be a string or an array of strings. If it's an array, the
     * combobox is considered
     * [multi-selectable](https://ariakit.org/examples/combobox-multiple).
     * @default ""
     */
    defaultSelectedValue?: ComboboxStoreState<T>["selectedValue"];
}
export interface ComboboxStoreProps<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue> extends ComboboxStoreOptions<T>, StoreProps<ComboboxStoreState<T>> {
}
export interface ComboboxStore<T extends ComboboxStoreSelectedValue = ComboboxStoreSelectedValue> extends ComboboxStoreFunctions<T>, Store<ComboboxStoreState<T>> {
}
export {};
