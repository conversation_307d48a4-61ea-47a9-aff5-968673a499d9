import type { CompositeSeparatorOptions } from "../composite/composite-separator.js";
import type { As, Props } from "../utils/types.js";
import type { ComboboxStore } from "./combobox-store.js";
/**
 * Returns props a `ComboboxSeparator` component for combobox items.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx
 * const store = useComboboxStore();
 * const props = useComboboxSeparator({ store });
 * <ComboboxPopover store={store}>
 *   <ComboboxItem value="Item 1" />
 *   <Role {...props} />
 *   <ComboboxItem value="Item 2" />
 *   <ComboboxItem value="Item 3" />
 * </ComboboxPopover>
 * ```
 */
export declare const useComboboxSeparator: import("../utils/types.js").Hook<ComboboxSeparatorOptions<"hr">>;
/**
 * Renders a divider between
 * [`ComboboxItem`](https://ariakit.org/reference/combobox-item) elements.
 * @see https://ariakit.org/components/combobox
 * @example
 * ```jsx {5}
 * <ComboboxProvider>
 *   <Combobox />
 *   <ComboboxPopover>
 *     <ComboboxItem value="Item 1" />
 *     <ComboboxSeparator />
 *     <ComboboxItem value="Item 2" />
 *     <ComboboxItem value="Item 3" />
 *   </ComboboxPopover>
 * </ComboboxProvider>
 * ```
 */
export declare const ComboboxSeparator: import("../utils/types.js").Component<ComboboxSeparatorOptions<"hr">>;
export interface ComboboxSeparatorOptions<T extends As = "hr"> extends CompositeSeparatorOptions<T> {
    /**
     * Object returned by the
     * [`useComboboxStore`](https://ariakit.org/reference/use-combobox-store)
     * hook. If not provided, the closest
     * [`ComboboxList`](https://ariakit.org/reference/combobox-list) or
     * [`ComboboxPopover`](https://ariakit.org/reference/combobox-popover)
     * components' context will be used.
     */
    store?: ComboboxStore;
}
export type ComboboxSeparatorProps<T extends As = "hr"> = Props<ComboboxSeparatorOptions<T>>;
