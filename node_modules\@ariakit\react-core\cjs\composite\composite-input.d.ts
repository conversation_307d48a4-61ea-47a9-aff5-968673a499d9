import type { As, Options, Props } from "../utils/types.js";
import type { CompositeStore } from "./composite-store.js";
/**
 * Returns props to create a `CompositeInput` component. This should be used in
 * conjunction with the `CompositeItem` component, the `useCompositeItem` hook,
 * or any other component/hook that uses `CompositeItem` underneath.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx
 * const store = useCompositeStore();
 * const props = useCompositeInput({ store });
 * <Composite store={store}>
 *   <CompositeItem {...props} />
 * </Composite>
 * ```
 */
export declare const useCompositeInput: import("../utils/types.js").Hook<CompositeInputOptions<"input">>;
/**
 * Renders an input as a composite item. This should be used in conjunction with
 * the [`CompositeItem`](https://ariakit.org/reference/composite-item) component
 * or a component that uses
 * [`CompositeItem`](https://ariakit.org/reference/composite-item) underneath.
 * @see https://ariakit.org/components/composite
 * @example
 * ```jsx {3}
 * <CompositeProvider>
 *   <Composite>
 *     <CompositeItem render={<CompositeInput />} />
 *   </Composite>
 * </CompositeProvider>
 * ```
 */
export declare const CompositeInput: import("../utils/types.js").Component<CompositeInputOptions<"input">>;
export interface CompositeInputOptions<T extends As = "input"> extends Options<T> {
    /**
     * Object returned by the
     * [`useCompositeStore`](https://ariakit.org/reference/use-composite-store)
     * hook. If not provided, the closest
     * [`Composite`](https://ariakit.org/reference/composite) or
     * [`CompositeProvider`](https://ariakit.org/reference/composite-provider)
     * components' context will be used.
     */
    store?: CompositeStore;
}
export type CompositeInputProps<T extends As = "input"> = Props<CompositeInputOptions<T>>;
