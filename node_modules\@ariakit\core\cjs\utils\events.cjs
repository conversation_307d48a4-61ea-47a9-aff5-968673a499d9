"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _YIUPKKEKcjs = require('../__chunks/YIUPKKEK.cjs');


var _5F4DVUNScjs = require('../__chunks/5F4DVUNS.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/utils/events.ts
function isPortalEvent(event) {
  return Boolean(
    event.currentTarget && !_5F4DVUNScjs.contains.call(void 0, event.currentTarget, event.target)
  );
}
function isSelfTarget(event) {
  return event.target === event.currentTarget;
}
function isOpeningInNewTab(event) {
  const element = event.currentTarget;
  if (!element)
    return false;
  const isAppleDevice = _YIUPKKEKcjs.isApple.call(void 0, );
  if (isAppleDevice && !event.metaKey)
    return false;
  if (!isAppleDevice && !event.ctrlKey)
    return false;
  const tagName = element.tagName.toLowerCase();
  if (tagName === "a")
    return true;
  if (tagName === "button" && element.type === "submit")
    return true;
  if (tagName === "input" && element.type === "submit")
    return true;
  return false;
}
function isDownloading(event) {
  const element = event.currentTarget;
  if (!element)
    return false;
  const tagName = element.tagName.toLowerCase();
  if (!event.altKey)
    return false;
  if (tagName === "a")
    return true;
  if (tagName === "button" && element.type === "submit")
    return true;
  if (tagName === "input" && element.type === "submit")
    return true;
  return false;
}
function fireEvent(element, type, eventInit) {
  const event = new Event(type, eventInit);
  return element.dispatchEvent(event);
}
function fireBlurEvent(element, eventInit) {
  const event = new FocusEvent("blur", eventInit);
  const defaultAllowed = element.dispatchEvent(event);
  const bubbleInit = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, eventInit), { bubbles: true });
  element.dispatchEvent(new FocusEvent("focusout", bubbleInit));
  return defaultAllowed;
}
function fireFocusEvent(element, eventInit) {
  const event = new FocusEvent("focus", eventInit);
  const defaultAllowed = element.dispatchEvent(event);
  const bubbleInit = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, eventInit), { bubbles: true });
  element.dispatchEvent(new FocusEvent("focusin", bubbleInit));
  return defaultAllowed;
}
function fireKeyboardEvent(element, type, eventInit) {
  const event = new KeyboardEvent(type, eventInit);
  return element.dispatchEvent(event);
}
function fireClickEvent(element, eventInit) {
  const event = new MouseEvent("click", eventInit);
  return element.dispatchEvent(event);
}
function isFocusEventOutside(event, container) {
  const containerElement = container || event.currentTarget;
  const relatedTarget = event.relatedTarget;
  return !relatedTarget || !_5F4DVUNScjs.contains.call(void 0, containerElement, relatedTarget);
}
function queueBeforeEvent(element, type, callback) {
  const raf = requestAnimationFrame(() => {
    element.removeEventListener(type, callImmediately, true);
    callback();
  });
  const callImmediately = () => {
    cancelAnimationFrame(raf);
    callback();
  };
  element.addEventListener(type, callImmediately, {
    once: true,
    capture: true
  });
  return raf;
}
function addGlobalEventListener(type, listener, options, scope = window) {
  const children = [];
  try {
    scope.document.addEventListener(type, listener, options);
    for (const frame of Array.from(scope.frames)) {
      children.push(addGlobalEventListener(type, listener, options, frame));
    }
  } catch (e) {
  }
  const removeEventListener = () => {
    try {
      scope.document.removeEventListener(type, listener, options);
    } catch (e) {
    }
    children.forEach((remove) => remove());
  };
  return removeEventListener;
}













exports.addGlobalEventListener = addGlobalEventListener; exports.fireBlurEvent = fireBlurEvent; exports.fireClickEvent = fireClickEvent; exports.fireEvent = fireEvent; exports.fireFocusEvent = fireFocusEvent; exports.fireKeyboardEvent = fireKeyboardEvent; exports.isDownloading = isDownloading; exports.isFocusEventOutside = isFocusEventOutside; exports.isOpeningInNewTab = isOpeningInNewTab; exports.isPortalEvent = isPortalEvent; exports.isSelfTarget = isSelfTarget; exports.queueBeforeEvent = queueBeforeEvent;
