"use client";
import {
  createCompositeStore
} from "../__chunks/IERTEJ3A.js";
import {
  createCollectionStore
} from "../__chunks/22K762VQ.js";
import {
  batch,
  createStore,
  setup,
  sync
} from "../__chunks/EAHJFCU4.js";
import {
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import "../__chunks/DLOEKDPY.js";
import "../__chunks/7PRQYBBV.js";
import {
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/tab/tab-store.ts
function createTabStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const composite = createCompositeStore(__spreadProps(__spreadValues({}, props), {
    orientation: defaultValue(
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "horizontal"
    ),
    focusLoop: defaultValue(props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
  const panels = createCollectionStore();
  const initialState = __spreadProps(__spreadValues({}, composite.getState()), {
    selectedId: defaultValue(
      props.selectedId,
      syncState == null ? void 0 : syncState.selectedId,
      props.defaultSelectedId,
      void 0
    ),
    selectOnMove: defaultValue(
      props.selectOnMove,
      syncState == null ? void 0 : syncState.selectOnMove,
      true
    )
  });
  const tab = createStore(initialState, composite, props.store);
  setup(
    tab,
    () => sync(tab, ["moves"], () => {
      const { activeId, selectOnMove } = tab.getState();
      if (!selectOnMove)
        return;
      if (!activeId)
        return;
      const tabItem = composite.item(activeId);
      if (!tabItem)
        return;
      if (tabItem.dimmed)
        return;
      if (tabItem.disabled)
        return;
      tab.setState("selectedId", tabItem.id);
    })
  );
  setup(
    tab,
    () => batch(
      tab,
      ["selectedId"],
      (state) => tab.setState("activeId", state.selectedId)
    )
  );
  setup(
    tab,
    () => sync(tab, ["selectedId", "renderedItems"], (state) => {
      if (state.selectedId !== void 0)
        return;
      const { activeId, renderedItems } = tab.getState();
      const tabItem = composite.item(activeId);
      if (tabItem && !tabItem.disabled && !tabItem.dimmed) {
        tab.setState("selectedId", tabItem.id);
      } else {
        const tabItem2 = renderedItems.find(
          (item) => !item.disabled && !item.dimmed
        );
        tab.setState("selectedId", tabItem2 == null ? void 0 : tabItem2.id);
      }
    })
  );
  setup(
    tab,
    () => sync(tab, ["renderedItems"], (state) => {
      const tabs = state.renderedItems;
      if (!tabs.length)
        return;
      return sync(panels, ["renderedItems"], (state2) => {
        const items = state2.renderedItems;
        const hasOrphanPanels = items.some((panel) => !panel.tabId);
        if (!hasOrphanPanels)
          return;
        items.forEach((panel, i) => {
          if (panel.tabId)
            return;
          const tabItem = tabs[i];
          if (!tabItem)
            return;
          panels.renderItem(__spreadProps(__spreadValues({}, panel), { tabId: tabItem.id }));
        });
      });
    })
  );
  return __spreadProps(__spreadValues(__spreadValues({}, composite), tab), {
    panels,
    setSelectedId: (id) => tab.setState("selectedId", id),
    select: (id) => {
      tab.setState("selectedId", id);
      composite.move(id);
    }
  });
}
export {
  createTabStore
};
