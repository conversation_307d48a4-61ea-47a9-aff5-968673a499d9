"use client";
import {
  createPopoverStore
} from "../__chunks/AF6IUUFN.js";
import "../__chunks/SX2XFD6A.js";
import "../__chunks/Z5IGYIPT.js";
import {
  createCompositeStore
} from "../__chunks/IERTEJ3A.js";
import "../__chunks/22K762VQ.js";
import {
  batch,
  createStore,
  mergeStore,
  omit,
  setup,
  sync,
  throwOnConflictingProps
} from "../__chunks/EAHJFCU4.js";
import {
  defaultValue
} from "../__chunks/Y3OOHFCN.js";
import "../__chunks/DLOEKDPY.js";
import {
  toArray
} from "../__chunks/7PRQYBBV.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../__chunks/4R3V3JGP.js";

// src/select/select-store.ts
function createSelectStore(_a = {}) {
  var _b = _a, {
    combobox
  } = _b, props = __objRest(_b, [
    "combobox"
  ]);
  const store = mergeStore(
    props.store,
    omit(combobox, [
      "value",
      "items",
      "renderedItems",
      "baseElement",
      "arrowElement",
      "anchorElement",
      "contentElement",
      "popoverElement",
      "disclosureElement"
    ])
  );
  throwOnConflictingProps(props, store);
  const syncState = store.getState();
  const composite = createCompositeStore(__spreadProps(__spreadValues({}, props), {
    store,
    virtualFocus: defaultValue(
      props.virtualFocus,
      syncState.virtualFocus,
      true
    ),
    includesBaseElement: defaultValue(
      props.includesBaseElement,
      syncState.includesBaseElement,
      false
    ),
    activeId: defaultValue(
      props.activeId,
      syncState.activeId,
      props.defaultActiveId,
      null
    ),
    orientation: defaultValue(
      props.orientation,
      syncState.orientation,
      "vertical"
    )
  }));
  const popover = createPopoverStore(__spreadProps(__spreadValues({}, props), {
    store,
    placement: defaultValue(
      props.placement,
      syncState.placement,
      "bottom-start"
    )
  }));
  const initialValue = new String("");
  const initialState = __spreadProps(__spreadValues(__spreadValues({}, composite.getState()), popover.getState()), {
    value: defaultValue(
      props.value,
      syncState.value,
      props.defaultValue,
      initialValue
    ),
    setValueOnMove: defaultValue(
      props.setValueOnMove,
      syncState.setValueOnMove,
      false
    ),
    selectElement: defaultValue(syncState.selectElement, null),
    labelElement: defaultValue(syncState.labelElement, null)
  });
  const select = createStore(initialState, composite, popover, store);
  setup(
    select,
    () => sync(select, ["value", "items"], (state) => {
      if (state.value !== initialValue)
        return;
      if (!state.items.length)
        return;
      const item = state.items.find(
        (item2) => !item2.disabled && item2.value != null
      );
      if ((item == null ? void 0 : item.value) == null)
        return;
      select.setState("value", item.value);
    })
  );
  setup(
    select,
    () => sync(select, ["mounted", "items", "value"], (state) => {
      if (combobox)
        return;
      if (state.mounted)
        return;
      const values = toArray(state.value);
      const lastValue = values[values.length - 1];
      if (lastValue == null)
        return;
      const item = state.items.find(
        (item2) => !item2.disabled && item2.value === lastValue
      );
      if (!item)
        return;
      select.setState("activeId", item.id);
    })
  );
  setup(
    select,
    () => batch(select, ["setValueOnMove", "moves"], (state) => {
      const { mounted, value, activeId } = select.getState();
      if (!state.setValueOnMove && mounted)
        return;
      if (Array.isArray(value))
        return;
      if (!state.moves)
        return;
      if (!activeId)
        return;
      const item = composite.item(activeId);
      if (!item || item.disabled || item.value == null)
        return;
      select.setState("value", item.value);
    })
  );
  return __spreadProps(__spreadValues(__spreadValues(__spreadValues({}, composite), popover), select), {
    combobox,
    setValue: (value) => select.setState("value", value),
    setSelectElement: (element) => select.setState("selectElement", element),
    setLabelElement: (element) => select.setState("labelElement", element)
  });
}
export {
  createSelectStore
};
