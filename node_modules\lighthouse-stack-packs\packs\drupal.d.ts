export const icon: "data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 186.525 243.713\"><path fill=\"%23009cde\" d=\"M131.64 51.91C114.491 34.769 98.13 18.429 93.26 0c-4.87 18.429-21.234 34.769-38.38 51.91C29.16 77.613 0 106.743 0 150.434a93.263 93.263 0 1 0 186.525 0c0-43.688-29.158-72.821-54.885-98.524m-92 120.256c-5.719-.194-26.824-36.571 12.329-75.303l25.909 28.3a2.215 2.215 0 0 1-.173 3.306c-6.183 6.34-32.534 32.765-35.81 41.902-.675 1.886-1.663 1.815-2.256 1.795m53.624 47.943a32.075 32.075 0 0 1-32.076-32.075 33.423 33.423 0 0 1 7.995-21.187c5.784-7.072 24.077-26.963 24.077-26.963s18.012 20.183 24.033 26.896a31.368 31.368 0 0 1 8.046 21.254 32.076 32.076 0 0 1-32.075 32.075m61.392-52.015c-.691 1.512-2.26 4.036-4.376 4.113-3.773.138-4.176-1.796-6.965-5.923-6.122-9.06-59.551-64.9-69.545-75.699-8.79-9.498-1.238-16.195 2.266-19.704 4.395-4.403 17.224-17.225 17.224-17.225s38.255 36.296 54.19 61.096 10.444 46.26 7.206 53.342\"/></svg>";
export const UIStrings: {
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by removing unused CSS, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'unused-css-rules': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by removing unused Javascript files in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'unused-javascript': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve image loading by using webp in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'modern-image-formats': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by lazy loading images that are initially offscreen in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'offscreen-images': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve site loading performance by reducing the total bytes delivered by their page in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'total-byte-weight': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by reducing the amount of render blocking resources present on their page, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'render-blocking-resources': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by minifying their CSS files in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'unminified-css': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by minifying their Javascript files in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'unminified-javascript': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by encoding animated images as video, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'efficient-animated-content': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve their site by enabling long caching in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'uses-long-cache-ttl': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve site performance by optimizing images, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'uses-optimized-images': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve performance by using responsive images in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'uses-responsive-images': string;
    /** Additional description of a Lighthouse audit that tells the user how they can improve the time to first byte speed metric, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'server-response-time': string;
    /** Additional description of a Lighthouse audit that tells the user how they can add preconnect or dns-prefetch resource hints, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'uses-rel-preconnect': string;
    /** Additional description of a Lighthouse audit that tells the user how they can specify font-display, in the context of the Drupal CMS platform. This is displayed after a user expands the section to see more. No character length limits. 'Learn More' becomes link text to additional documentation. */
    'font-display': string;
};
export declare let id: string;
export declare let title: string;
